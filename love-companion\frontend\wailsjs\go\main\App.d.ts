// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CompleteWishlist(arg1:string):Promise<void>;

export function CreateAnniversary(arg1:string,arg2:string,arg3:string,arg4:boolean,arg5:number,arg6:string):Promise<any>;

export function CreateDiary(arg1:string,arg2:string,arg3:string,arg4:Array<string>):Promise<any>;

export function CreateMoodRecord(arg1:string,arg2:number,arg3:string,arg4:Array<string>):Promise<any>;

export function CreatePhoto(arg1:string,arg2:string,arg3:string,arg4:string,arg5:Array<string>):Promise<any>;

export function CreateWishlist(arg1:string,arg2:string,arg3:string,arg4:number,arg5:number,arg6:string):Promise<any>;

export function DeleteDiary(arg1:string):Promise<void>;

export function DeletePhoto(arg1:string):Promise<void>;

export function GetAllAnniversaries():Promise<any>;

export function GetAllDiaries():Promise<any>;

export function GetAllMoods():Promise<any>;

export function GetAllPhotos():Promise<any>;

export function GetAllWishlists():Promise<any>;

export function GetCurrentUser():Promise<any>;

export function GetDaysCount():Promise<number>;

export function GetMoodRecords(arg1:string):Promise<any>;

export function GetSettings():Promise<any>;

export function Greet(arg1:string):Promise<string>;

export function InitUser(arg1:string,arg2:string,arg3:string):Promise<void>;

export function SaveSettings(arg1:string,arg2:string,arg3:string,arg4:string,arg5:string,arg6:string,arg7:boolean,arg8:boolean,arg9:boolean):Promise<void>;

export function UpdateDiary(arg1:string,arg2:string,arg3:string,arg4:string,arg5:Array<string>):Promise<void>;

export function UpdatePhoto(arg1:string,arg2:string,arg3:string,arg4:string,arg5:Array<string>):Promise<void>;
