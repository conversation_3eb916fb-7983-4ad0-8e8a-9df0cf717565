# Love Companion 故障排除指南

## 🚨 常见问题解决方案

### 问题1: "加载活动记录时出现问题"

#### 可能原因和解决方案：

**1. Redis服务未启动**
```bash
# 检查Redis是否运行
redis-cli ping

# 如果返回PONG，说明Redis正常
# 如果连接失败，启动Redis服务
redis-server redis.conf
```

**2. 用户信息未初始化**
- 症状：显示"用户未初始化"
- 解决：重新运行用户初始化流程
- 或者运行数据迁移工具：`migrate-data.bat`

**3. 环境变量未设置**
```bash
# Windows
set REDIS_URL=redis://localhost:6379

# PowerShell
$env:REDIS_URL="redis://localhost:6379"
```

**4. 数据库连接问题**
- 检查Redis配置文件 `redis.conf`
- 确认端口6379未被占用
- 检查防火墙设置

### 问题2: API调用失败

#### 错误类型和解决方案：

**"Wails运行时不可用"**
- 等待应用完全加载
- 刷新页面重试
- 检查是否在开发模式下运行

**"Redis数据库连接失败"**
- 启动Redis服务
- 检查Redis配置
- 验证连接字符串

**"用户信息获取失败"**
- 重新初始化用户
- 运行数据迁移工具
- 检查用户数据完整性

### 问题3: 数据不显示或为空

#### 排查步骤：

1. **使用系统检查功能**
   - 点击活动区域的"系统检查"按钮
   - 查看各项状态指示

2. **检查数据完整性**
   ```bash
   # 连接Redis查看数据
   redis-cli
   > keys user:*
   > get user:<user_id>
   ```

3. **重新创建测试数据**
   - 添加一条日记
   - 记录一次心情
   - 观察活动列表是否更新

## 🔧 系统检查工具

### 内置诊断功能

应用提供了内置的系统状态检查：

1. **自动检查项目**：
   - Redis数据库连接
   - 用户信息状态
   - API服务连接
   - 各类数据获取

2. **使用方法**：
   - 当活动加载失败时，点击"系统检查"按钮
   - 查看详细的状态报告
   - 根据提示进行相应操作

### 手动检查步骤

**1. 检查Redis服务**
```bash
# 测试连接
redis-cli ping

# 查看Redis日志
redis-cli monitor

# 检查内存使用
redis-cli info memory
```

**2. 检查应用日志**
- 打开浏览器开发者工具 (F12)
- 查看Console标签页的错误信息
- 查看Network标签页的API调用状态

**3. 检查数据结构**
```bash
redis-cli
> keys *
> type user:index
> smembers user:index
```

## 🛠️ 修复工具

### 数据迁移工具
```bash
# 运行数据迁移和修复
migrate-data.bat
```

### 重置用户数据
```bash
redis-cli
> del user:index
> keys user:* | xargs del
```

### 清理缓存
```bash
# 清理Redis缓存
redis-cli flushdb

# 重启应用
# 重新初始化用户
```

## 📋 错误代码对照表

| 错误信息 | 错误代码 | 解决方案 |
|---------|---------|---------|
| Redis连接失败 | REDIS_001 | 启动Redis服务 |
| 用户信息获取失败 | USER_001 | 重新初始化用户 |
| API调用失败 | API_001 | 检查Wails运行时 |
| 数据获取失败 | DATA_001 | 检查数据完整性 |
| Wails运行时不可用 | RUNTIME_001 | 等待应用加载完成 |

## 🆘 获取帮助

### 收集诊断信息

在寻求帮助时，请提供以下信息：

1. **系统信息**：
   - 操作系统版本
   - Redis版本
   - 应用版本

2. **错误信息**：
   - 完整的错误消息
   - 浏览器控制台日志
   - 系统检查结果

3. **重现步骤**：
   - 详细的操作步骤
   - 预期结果vs实际结果

### 联系方式

- 查看应用日志文件
- 运行系统检查获取详细报告
- 提供完整的错误信息和环境配置

## 🔄 预防措施

### 定期维护

1. **定期备份数据**
2. **监控Redis服务状态**
3. **保持应用更新**
4. **定期清理日志文件**

### 最佳实践

1. **启动顺序**：先启动Redis，再启动应用
2. **环境变量**：确保REDIS_URL正确设置
3. **数据验证**：定期检查数据完整性
4. **错误监控**：关注控制台错误信息
