<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Companion - 爱情伴侣</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome 6.4.0 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: auto; /* 允许页面滚动，修复内容被遮挡问题 */
        }

        /* 主题样式 */
        body.theme-romantic { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        body.theme-purple  { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
        body.theme-blue    { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        body.theme-gradient{ background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%); }

        .glass-morphism {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
            z-index: 1;
        }

        .heart {
            position: absolute;
            color: rgba(255, 192, 203, 0.6);
            animation: float 6s infinite linear;
            font-size: 20px;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            15% {
                opacity: 1;
            }
            85% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) scale(1);
                opacity: 0;
            }
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 5px 0;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(5px);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .hidden {
            display: none !important;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: #333;
            font-size: 0.95rem;
            margin-bottom: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            background: rgba(255, 255, 255, 0.2);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 浮动爱心背景 -->
    <div class="floating-hearts" id="floatingHearts"></div>

    <!-- 首次使用初始化模态框 -->
    <div id="initModal" class="modal">
        <div class="modal-content">
            <h2 class="text-2xl font-bold mb-6 text-center gradient-text">欢迎使用 Love Companion</h2>
            <form id="initForm">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2">你的姓名</label>
                    <input type="text" id="userName" class="form-input" placeholder="请输入你的姓名" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2">伴侣姓名</label>
                    <input type="text" id="partnerName" class="form-input" placeholder="请输入伴侣姓名" required>
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">开始交往日期</label>
                    <input type="date" id="startDate" class="form-input" required>
                </div>
                <button type="submit" class="btn-primary w-full">
                    <span id="initBtnText">开始使用</span>
                    <div id="initLoading" class="loading hidden"></div>
                </button>
            </form>
        </div>
    </div>

    <div class="flex h-screen relative z-10">
        <!-- 侧边栏 -->
        <div class="sidebar w-64 p-6">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold text-white mb-2">
                    <i class="fas fa-heart text-pink-300 mr-2"></i>
                    Love Companion
                </h1>
                <p class="text-white/80 text-sm">爱情伴侣</p>
            </div>

            <nav class="space-y-2">
                <a href="#" class="nav-item active flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('dashboard')">
                    <i class="fas fa-home w-5 mr-3"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('diary')">
                    <i class="fas fa-book w-5 mr-3"></i>
                    <span>恋爱日记</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('memories')">
                    <i class="fas fa-calendar-heart w-5 mr-3"></i>
                    <span>纪念日</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('photos')">
                    <i class="fas fa-images w-5 mr-3"></i>
                    <span>照片相册</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('mood')">
                    <i class="fas fa-smile w-5 mr-3"></i>
                    <span>心情记录</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('wishlist')">
                    <i class="fas fa-star w-5 mr-3"></i>
                    <span>愿望清单</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white hover:text-pink-200" onclick="showPage('settings')">
                    <i class="fas fa-cog w-5 mr-3"></i>
                    <span>设置</span>
                </a>
            </nav>

            <div class="mt-auto pt-6">
                <div class="glass-card p-4 text-center">
                    <div class="text-white mb-2">
                        <i class="fas fa-heart text-pink-300 text-2xl mb-2"></i>
                        <p class="text-sm">在一起</p>
                        <p class="text-lg font-bold" id="daysCounter">0天</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 content-area">
            <!-- 首页 Dashboard -->
            <div id="dashboard" class="page p-8">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-white mb-2">欢迎回来！</h2>
                    <p class="text-white/80">记录你们的美好时光</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="glass-card p-6 cursor-pointer" onclick="showPage('diary')">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-book text-pink-300 text-2xl mr-3"></i>
                            <h3 class="text-white text-lg font-semibold">今日日记</h3>
                        </div>
                        <p class="text-white/80 mb-4">记录今天的美好瞬间</p>
                        <div class="flex items-center text-pink-300">
                            <span class="mr-2">写日记</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>

                    <div class="glass-card p-6 cursor-pointer" onclick="showPage('mood')">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-smile text-yellow-300 text-2xl mr-3"></i>
                            <h3 class="text-white text-lg font-semibold">心情记录</h3>
                        </div>
                        <p class="text-white/80 mb-4">记录当前心情状态</p>
                        <div class="flex items-center text-yellow-300">
                            <span class="mr-2">记录心情</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>

                    <div class="glass-card p-6 cursor-pointer" onclick="showPage('wishlist')">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-star text-purple-300 text-2xl mr-3"></i>
                            <h3 class="text-white text-lg font-semibold">愿望清单</h3>
                        </div>
                        <p class="text-white/80 mb-4">添加新的愿望目标</p>
                        <div class="flex items-center text-purple-300">
                            <span class="mr-2">查看愿望</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </div>

                <div class="glass-card p-6">
                    <h3 class="text-white text-xl font-semibold mb-4">最近活动</h3>
                    <div id="recentActivities" class="space-y-4">
                        <p class="text-white/60 text-center py-8">暂无活动记录</p>
                    </div>
                </div>
            </div>

            <!-- 恋爱日记页面 -->
            <div id="diary" class="page p-8 hidden">
                <div class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">恋爱日记</h2>
                        <p class="text-white/80">记录你们的美好时光</p>
                    </div>
                    <button class="btn-primary" onclick="showWriteDiary()">
                        <i class="fas fa-plus mr-2"></i>
                        写新日记
                    </button>
                </div>

                <div class="glass-card p-6">
                    <div id="diaryList">
                        <p class="text-white/60 text-center py-8">还没有日记，快写下第一篇吧！</p>
                    </div>
                </div>
            </div>

            <!-- 写日记模态框 -->
            <div id="diaryModal" class="modal hidden">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold gradient-text">写日记</h2>
                        <button onclick="closeDiaryModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="diaryForm">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">标题</label>
                            <input type="text" id="diaryTitle" class="form-input" placeholder="给今天起个标题吧" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">内容</label>
                            <textarea id="diaryContent" class="form-input form-textarea" placeholder="记录下今天发生的美好事情..." required></textarea>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">心情</label>
                            <select id="diaryMood" class="form-input">
                                <option value="happy">😊 开心</option>
                                <option value="love">💖 甜蜜</option>
                                <option value="excited">🎉 兴奋</option>
                                <option value="peaceful">😌 平静</option>
                                <option value="romantic">🌹 浪漫</option>
                                <option value="grateful">🙏 感恩</option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-medium mb-2">标签 (用逗号分隔)</label>
                            <input type="text" id="diaryTags" class="form-input" placeholder="约会,电影,晚餐">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeDiaryModal()" class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">保存日记</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 纪念日页面 -->
            <div id="memories" class="page p-8 hidden">
                <div class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">纪念日管理</h2>
                        <p class="text-white/80">记录重要的日子</p>
                    </div>
                    <button class="btn-primary" onclick="showAddMemory()">
                        <i class="fas fa-plus mr-2"></i>
                        添加纪念日
                    </button>
                </div>

                <div class="glass-card p-6">
                    <div id="memoryList">
                        <p class="text-white/60 text-center py-8">还没有纪念日，快添加一个重要日子吧！</p>
                    </div>
                </div>
            </div>

            <!-- 添加纪念日模态框 -->
            <div id="memoryModal" class="modal hidden">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold gradient-text">添加纪念日</h2>
                        <button onclick="closeMemoryModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="memoryForm">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">标题</label>
                            <input type="text" id="memoryTitle" class="form-input" placeholder="纪念日名称" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">描述</label>
                            <textarea id="memoryDescription" class="form-input" style="min-height: 80px;" placeholder="描述这个特殊的日子..."></textarea>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">日期</label>
                            <input type="date" id="memoryDate" class="form-input" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">分类</label>
                            <select id="memoryCategory" class="form-input">
                                <option value="love">💖 恋爱相关</option>
                                <option value="birthday">🎂 生日</option>
                                <option value="anniversary">💍 纪念日</option>
                                <option value="holiday">🎉 节日</option>
                                <option value="special">⭐ 特殊日子</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">提前提醒天数</label>
                            <input type="number" id="memoryRemind" class="form-input" value="3" min="0" max="30">
                        </div>
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" id="memoryRecurring" class="mr-2">
                                <span class="text-gray-700 text-sm">每年重复</span>
                            </label>
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeMemoryModal()" class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">添加纪念日</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 照片相册页面 -->
            <div id="photos" class="page p-8 hidden">
                <div class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">照片相册</h2>
                        <p class="text-white/80">保存美好回忆</p>
                    </div>
                    <button onclick="openPhotoModal()" class="btn-primary">
                        <i class="fas fa-upload mr-2"></i>
                        上传照片
                    </button>
                </div>

                <div class="glass-card p-6">
                    <div id="photoGallery" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <p class="col-span-full text-white/60 text-center py-8">还没有照片，上传第一张回忆吧！</p>
                    </div>
                </div>
            </div>

            <!-- 心情记录页面 -->
            <div id="mood" class="page p-8 hidden">
                <div class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">心情记录</h2>
                        <p class="text-white/80">追踪你的情感状态</p>
                    </div>
                    <button class="btn-primary" onclick="showAddMood()">
                        <i class="fas fa-plus mr-2"></i>
                        记录心情
                    </button>
                </div>

                <div class="glass-card p-6">
                    <div id="moodList">
                        <p class="text-white/60 text-center py-8">还没有心情记录，记录下现在的心情吧！</p>
                    </div>
                </div>
            </div>

            <!-- 记录心情模态框 -->
            <div id="moodModal" class="modal hidden">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold gradient-text">记录心情</h2>
                        <button onclick="closeMoodModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="moodForm">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">心情类型</label>
                            <select id="moodType" class="form-input">
                                <option value="happy">😊 开心</option>
                                <option value="love">💖 恋爱</option>
                                <option value="excited">🎉 兴奋</option>
                                <option value="peaceful">😌 平静</option>
                                <option value="sad">😢 伤心</option>
                                <option value="anxious">😰 焦虑</option>
                                <option value="grateful">🙏 感恩</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">强度 (1-10)</label>
                            <input type="range" id="moodIntensity" class="w-full" min="1" max="10" value="5">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>很轻微</span>
                                <span id="intensityValue">5</span>
                                <span>非常强烈</span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">心情描述</label>
                            <textarea id="moodDescription" class="form-input" style="min-height: 80px;" placeholder="描述一下现在的心情..."></textarea>
                        </div>
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-medium mb-2">影响因素 (用逗号分隔)</label>
                            <input type="text" id="moodFactors" class="form-input" placeholder="工作,天气,约会">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeMoodModal()" class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">记录心情</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 愿望清单页面 -->
            <div id="wishlist" class="page p-8 hidden">
                <div class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">愿望清单</h2>
                        <p class="text-white/80">规划你们的共同目标</p>
                    </div>
                    <button class="btn-primary" onclick="showAddWish()">
                        <i class="fas fa-plus mr-2"></i>
                        添加愿望
                    </button>
                </div>

                <div class="glass-card p-6">
                    <div id="wishList">
                        <p class="text-white/60 text-center py-8">还没有愿望，添加一个共同目标吧！</p>
                    </div>
                </div>
            </div>

            <!-- 添加愿望模态框 -->
            <div id="wishModal" class="modal hidden">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold gradient-text">添加愿望</h2>
                        <button onclick="closeWishModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="wishForm">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">愿望标题</label>
                            <input type="text" id="wishTitle" class="form-input" placeholder="我们的愿望是..." required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">详细描述</label>
                            <textarea id="wishDescription" class="form-input" style="min-height: 80px;" placeholder="详细描述这个愿望..."></textarea>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">分类</label>
                            <select id="wishCategory" class="form-input">
                                <option value="travel">✈️ 旅行</option>
                                <option value="gift">🎁 礼物</option>
                                <option value="activity">🎯 活动</option>
                                <option value="home">🏠 居家</option>
                                <option value="career">💼 事业</option>
                                <option value="other">📝 其他</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">优先级 (1-5)</label>
                            <input type="range" id="wishPriority" class="w-full" min="1" max="5" value="3">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>一般</span>
                                <span id="priorityValue">3</span>
                                <span>非常重要</span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">预算 (可选)</label>
                            <input type="number" id="wishCost" class="form-input" placeholder="0" min="0">
                        </div>
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-medium mb-2">目标日期 (可选)</label>
                            <input type="date" id="wishTargetDate" class="form-input">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeWishModal()" class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">添加愿望</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 照片上传模态框 -->
            <div id="photoModal" class="modal hidden">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold gradient-text">上传照片</h2>
                        <button onclick="closePhotoModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="photoForm">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">选择照片</label>
                            <input type="file" id="photoFile" class="form-input" accept="image/*" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">照片标题</label>
                            <input type="text" id="photoTitle" class="form-input" placeholder="给这张照片起个名字..." required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">描述</label>
                            <textarea id="photoDescription" class="form-input" style="min-height: 80px;" placeholder="记录这个美好瞬间..."></textarea>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-medium mb-2">分类</label>
                            <select id="photoCategory" class="form-input">
                                <option value="daily">📅 日常</option>
                                <option value="travel">🌍 旅行</option>
                                <option value="food">🍽️ 美食</option>
                                <option value="celebration">🎉 庆祝</option>
                                <option value="romantic">💕 浪漫</option>
                                <option value="family">👨‍👩‍👧‍👦 家庭</option>
                                <option value="pets">🐕 宠物</option>
                                <option value="other">📷 其他</option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-medium mb-2">标签 (用逗号分隔)</label>
                            <input type="text" id="photoTags" class="form-input" placeholder="例如: 生日, 快乐, 蛋糕">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="closePhotoModal()" class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">上传照片</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings" class="page p-8 hidden">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-white mb-2">设置</h2>
                    <p class="text-white/80">个性化你的应用</p>
                </div>

                <div class="space-y-6">
                    <!-- 基本设置 -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">基本设置</h3>
                        <form id="settingsForm" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-white/80 text-sm font-medium mb-2">应用主题</label>
                                    <select id="settingsTheme" class="form-input">
                                        <option value="romantic">💕 浪漫粉</option>
                                        <option value="purple">💜 梦幻紫</option>
                                        <option value="blue">💙 天空蓝</option>
                                        <option value="gradient">🌈 渐变色</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-white/80 text-sm font-medium mb-2">语言</label>
                                    <select id="settingsLanguage" class="form-input">
                                        <option value="zh-CN">🇨🇳 简体中文</option>
                                        <option value="en-US">🇺🇸 English</option>
                                        <option value="ja-JP">🇯🇵 日本語</option>
                                    </select>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-white/80 text-sm font-medium mb-2">字体大小</label>
                                    <select id="settingsFontSize" class="form-input">
                                        <option value="small">小</option>
                                        <option value="medium">中</option>
                                        <option value="large">大</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-white/80 text-sm font-medium mb-2">显示密度</label>
                                    <select id="settingsDensity" class="form-input">
                                        <option value="comfortable">舒适</option>
                                        <option value="compact">紧凑</option>
                                        <option value="cozy">宽松</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 通知设置 -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">通知设置</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                                <div>
                                    <div class="text-white font-medium">纪念日提醒</div>
                                    <div class="text-white/60 text-sm">在重要日子前提醒你</div>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="anniversaryNotifications" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-500"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                                <div>
                                    <div class="text-white font-medium">每日记录提醒</div>
                                    <div class="text-white/60 text-sm">提醒你记录今天的美好时光</div>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="dailyReminders" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-500"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 隐私设置 -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">隐私设置</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                                <div>
                                    <div class="text-white font-medium">数据存储方式</div>
                                    <div class="text-white/60 text-sm">选择本地存储或云端存储</div>
                                </div>
                                <select id="storageType" class="form-input bg-white/10">
                                    <option value="local">本地存储</option>
                                    <option value="redis">Redis存储</option>
                                </select>
                            </div>
                            <!-- Redis 连接设置（当选择 Redis 时显示） -->
                            <div id="redisConfig" class="p-3 bg-white/5 rounded-lg hidden">
                                <label class="block text-white/80 text-sm font-medium mb-2">Redis 连接 URL</label>
                                <input id="redisURL" class="form-input w-full" placeholder="redis://:password@host:6379/0" />
                                <p class="text-white/60 text-xs mt-1">示例：redis://127.0.0.1:6379 或 redis://:pwd@localhost:6379/0</p>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                                <div>
                                    <div class="text-white font-medium">自动备份</div>
                                    <div class="text-white/60 text-sm">定期备份你的数据</div>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="autoBackup" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-500"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-4">
                        <button onclick="saveSettings()" class="btn-primary flex-1">
                            <i class="fas fa-save mr-2"></i>
                    <script>
                        // 根据存储类型切换显示Redis配置
                        document.addEventListener('change', (e) => {
                            if (e.target && e.target.id === 'storageType') {
                                document.getElementById('redisConfig').classList.toggle('hidden', e.target.value !== 'redis');
                            }
                        });
                    </script>

                            保存设置
                        </button>
                        <button onclick="resetSettings()" class="btn-secondary flex-1">
                            <i class="fas fa-undo mr-2"></i>
                            重置默认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let isInitialized = false;

        // Wails API 封装
        const api = {
            async call(method, ...args) {
                try {
                    // 检查Wails运行时是否可用
                    if (!window.go || !window.go.main || !window.go.main.App) {
                        throw new Error('Wails运行时不可用');
                    }
                    return await window.go.main.App[method](...args);
                } catch (error) {
                    console.error(`API调用失败: ${method}`, error);
                    throw error;
                }
            }
        };

        // 初始化应用
        async function initApp() {
            try {
                console.log('开始初始化应用...');
                
                // 检查用���是否已存在
                const user = await api.call('GetCurrentUser');
                console.log('获取用户信息:', user);
                
                // 检查用户信息完整性（包括name、partner、start_date字段）
                if (user && user.name && user.partner && user.start_date) {
                    console.log('用户信息完整，自动登录');
                    currentUser = user;
                    isInitialized = true;
                    document.getElementById('initModal').style.display = 'none';
                    await updateDaysCounter();
                    await loadDashboardData();
                    console.log('自动登录成功');
                } else {
                    console.log('用户信息不完整或不存在，显示初始化界面');
                    // 显示初始化模态框
                    document.getElementById('initModal').style.display = 'flex';
                }
                
                // 应用已保存的主题与语言 - 启动时自动加载设置
                try {
                    console.log('启动时加载设置...');
                    const settings = await api.call('GetSettings');
                    console.log('启动时获取的设置:', settings);
                    
                    if (settings) {
                        // 应用主题和语言
                        applyTheme(settings.theme || 'romantic');
                        applyLanguage(settings.language || 'zh-CN');
                        console.log(`应用启动设置: 主题=${settings.theme}, 语言=${settings.language}`);
                    } else {
                        console.log('启动时没有找到设置数据，使用默认设置');
                        applyTheme('romantic');
                        applyLanguage('zh-CN');
                    }
                } catch(e) {
                    console.log('启动时加载设置失败，使用默认设置:', e);
                    applyTheme('romantic');
                    applyLanguage('zh-CN');
                }

            } catch (error) {
                console.log('获取用户信息失败，显示初始化界面:', error);
                // 用户不存在，显示初始化模态框
                document.getElementById('initModal').style.display = 'flex';
            }
        }

        // 处理用户初始化表单
        document.getElementById('initForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const name = document.getElementById('userName').value.trim();
            const partner = document.getElementById('partnerName').value.trim();
            const startDate = document.getElementById('startDate').value;

            if (!name || !partner || !startDate) {
                alert('请填写完整信息');
                return;
            }

            const initBtn = document.getElementById('initBtnText');
            const loading = document.getElementById('initLoading');

            try {
                initBtn.style.display = 'none';
                loading.classList.remove('hidden');

                await api.call('InitUser', name, partner, startDate);

                document.getElementById('initModal').style.display = 'none';
                isInitialized = true;

                // 获取用户信息
                currentUser = await api.call('GetCurrentUser');
                await updateDaysCounter();
                await loadDashboardData();

                alert('初始化成功！欢迎使用 Love Companion');

            } catch (error) {
                alert('初始化失败: ' + error.message);
            } finally {
                initBtn.style.display = 'inline';
                loading.classList.add('hidden');
            }
        });

        // 更新在一起天数
        async function updateDaysCounter() {
            try {
                const days = await api.call('GetDaysCount');
                document.getElementById('daysCounter').textContent = days + '天';
            } catch (error) {
                console.error('获取天数失败:', error);
            }
        }

        // 加载仪表盘数据
        async function loadDashboardData() {
            try {
                // 更新欢迎信息
                if (currentUser) {
                    const welcome = document.querySelector('#dashboard h2');
                    welcome.textContent = `欢迎回来，${currentUser.name || currentUser.Name}！`;
                }

                // 加载最近活动
                await loadRecentActivities();
            } catch (error) {
                console.error('加载仪表盘数据失败:', error);
            }
        }

        // 加载最近活动
        async function loadRecentActivities() {
            try {
                // 并行获取各种活动数据
                const [diaries, anniversaries, moods, wishes] = await Promise.all([
                    api.call('GetAllDiaries').catch(() => []),
                    api.call('GetAllAnniversaries').catch(() => []),
                    api.call('GetAllMoods').catch(() => []),
                    api.call('GetAllWishlists').catch(() => [])
                ]);

                const activities = [];

                // 添加日记活动
                if (diaries && diaries.length > 0) {
                    diaries.slice(0, 3).forEach(diary => {
                        activities.push({
                            type: 'diary',
                            title: `📝 记录了日记: ${diary.title}`,
                            time: diary.created_at,
                            icon: 'fas fa-book',
                            color: 'text-blue-400'
                        });
                    });
                }

                // 添加纪念日活动
                if (anniversaries && anniversaries.length > 0) {
                    anniversaries.slice(0, 2).forEach(anniversary => {
                        activities.push({
                            type: 'anniversary',
                            title: `🎉 纪念日: ${anniversary.title}`,
                            time: anniversary.date,
                            icon: 'fas fa-heart',
                            color: 'text-pink-400'
                        });
                    });
                }

                // 添加心情活动
                if (moods && moods.length > 0) {
                    moods.slice(0, 2).forEach(mood => {
                        activities.push({
                            type: 'mood',
                            title: `${getMoodEmoji(mood.mood)} 心情: ${mood.mood}`,
                            time: mood.created_at,
                            icon: 'fas fa-smile',
                            color: 'text-yellow-400'
                        });
                    });
                }

                // 添加愿望活动
                if (wishes && wishes.length > 0) {
                    wishes.filter(w => w.is_completed).slice(0, 1).forEach(wish => {
                        activities.push({
                            type: 'wish',
                            title: `⭐ 完成愿望: ${wish.title}`,
                            time: wish.completed_at,
                            icon: 'fas fa-star',
                            color: 'text-green-400'
                        });
                    });
                }

                // 按时间排序
                activities.sort((a, b) => new Date(b.time) - new Date(a.time));

                // 显示活动
                const activitiesContainer = document.getElementById('recentActivities');
                if (activities.length === 0) {
                    activitiesContainer.innerHTML = '<p class="text-white/60 text-center py-8">还没有活动记录，快开始记录你们的美好时光吧！</p>';
                } else {
                    activitiesContainer.innerHTML = activities.slice(0, 5).map(activity => `
                        <div class="bg-white/5 p-4 rounded-lg border-l-4 border-pink-400/50">
                            <div class="flex items-center space-x-3">
                                <i class="${activity.icon} ${activity.color}"></i>
                                <div class="flex-1">
                                    <p class="text-white text-sm">${activity.title}</p>
                                    <p class="text-white/50 text-xs mt-1">${formatTime(activity.time)}</p>
                                </div>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载最近活动失败:', error);
                const activitiesContainer = document.getElementById('recentActivities');
                activitiesContainer.innerHTML = '<p class="text-white/60 text-center py-8">加载活动记录时出现问题</p>';
            }
        }

        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.add('hidden'));

            // 显示选中的页面
            document.getElementById(pageId).classList.remove('hidden');

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.closest('.nav-item').classList.add('active');

            // 根据页面加载相应数据
            switch(pageId) {
                case 'diary':
                    loadDiaries();
                    break;
                case 'memories':
                    loadMemories();
                    break;
                case 'photos':
                    loadPhotos();
                    break;
                case 'mood':
                    loadMoodPage();
                    break;
                case 'wishlist':
                    loadWishes();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // 日记相关功能
        function showWriteDiary() {
            document.getElementById('diaryModal').classList.remove('hidden');
        }

        function closeDiaryModal() {
            document.getElementById('diaryModal').classList.add('hidden');
            document.getElementById('diaryForm').reset();
        }

        async function loadDiaries() {
            try {
                const diaries = await api.call('GetAllDiaries');
                const diaryList = document.getElementById('diaryList');

                if (!diaries || diaries.length === 0) {
                    diaryList.innerHTML = '<p class="text-white/60 text-center py-8">还没有日记，快写下第一篇吧！</p>';
                    return;
                }

                diaryList.innerHTML = diaries.map(diary => `
                    <div class="bg-white/10 p-4 rounded-lg mb-4">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="text-white font-semibold">${diary.title}</h4>
                            <span class="text-white/60 text-sm">${formatDate(diary.created_at)}</span>
                        </div>
                        <p class="text-white/80 text-sm mb-3">${diary.content.substring(0, 100)}${diary.content.length > 100 ? '...' : ''}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-pink-300">
                                <i class="fas fa-heart mr-1"></i>
                                <span class="text-sm">${getMoodEmoji(diary.mood)} ${diary.mood}</span>
                            </div>
                            <button onclick="deleteDiary('${diary.id}')" class="text-red-400 hover:text-red-300">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载日记失败:', error);
            }
        }

        document.getElementById('diaryForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const title = document.getElementById('diaryTitle').value.trim();
            const content = document.getElementById('diaryContent').value.trim();
            const mood = document.getElementById('diaryMood').value;
            const tagsStr = document.getElementById('diaryTags').value.trim();
            const tags = tagsStr ? tagsStr.split(',').map(t => t.trim()) : [];

            try {
                await api.call('CreateDiary', title, content, mood, tags);
                closeDiaryModal();
                loadDiaries();
                alert('日记保存成功！');
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        });

        async function deleteDiary(diaryId) {
            if (confirm('确定要删除这篇日记吗？')) {
                try {
                    await api.call('DeleteDiary', diaryId);
                    loadDiaries();
                } catch (error) {
                    alert('删除失败: ' + error.message);
                }
            }
        }

        // 纪念日相关功能
        function showAddMemory() {
            document.getElementById('memoryModal').classList.remove('hidden');
        }

        function closeMemoryModal() {
            document.getElementById('memoryModal').classList.add('hidden');
            document.getElementById('memoryForm').reset();
        }

        async function loadMemories() {
            try {
                const memories = await api.call('GetAllAnniversaries');
                const memoryList = document.getElementById('memoryList');

                if (!memories || memories.length === 0) {
                    memoryList.innerHTML = '<p class="text-white/60 text-center py-8">还没有纪念日，快添加一个重要日子吧！</p>';
                    return;
                }

                memoryList.innerHTML = memories.map(memory => `
                    <div class="bg-white/10 p-4 rounded-lg mb-4">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="text-white font-semibold">${memory.title}</h4>
                            <span class="text-white/60 text-sm">${formatDate(memory.date)}</span>
                        </div>
                        <p class="text-white/80 text-sm mb-3">${memory.description}</p>
                        <div class="flex items-center text-blue-300">
                            <i class="fas fa-calendar mr-1"></i>
                            <span class="text-sm">${getCategoryEmoji(memory.category)} ${memory.category}</span>
                            ${memory.is_recurring ? '<span class="ml-2 text-xs bg-green-500 px-2 py-1 rounded">重复</span>' : ''}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载纪念日失败:', error);
            }
        }

        document.getElementById('memoryForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const title = document.getElementById('memoryTitle').value.trim();
            const description = document.getElementById('memoryDescription').value.trim();
            const date = document.getElementById('memoryDate').value;
            const category = document.getElementById('memoryCategory').value;
            const remindDays = parseInt(document.getElementById('memoryRemind').value);
            const isRecurring = document.getElementById('memoryRecurring').checked;

            try {
                await api.call('CreateAnniversary', title, description, date, isRecurring, remindDays, category);
                closeMemoryModal();
                loadMemories();
                alert('纪念日添加成功！');
            } catch (error) {
                alert('添加失败: ' + error.message);
            }
        });

        // 心情记录相关功能
        function showAddMood() {
            document.getElementById('moodModal').classList.remove('hidden');
        }

        function closeMoodModal() {
            document.getElementById('moodModal').classList.add('hidden');
            document.getElementById('moodForm').reset();
        }

        async function loadMoods() {
            try {
                const today = new Date().toISOString().split('T')[0];
                const moods = await api.call('GetMoodRecords', today);
                const moodList = document.getElementById('moodList');

                if (!moods || moods.length === 0) {
                    moodList.innerHTML = '<p class="text-white/60 text-center py-8">还没有心情记录，记录下现在的心情吧！</p>';
                    return;
                }

                moodList.innerHTML = moods.map(mood => `
                    <div class="bg-white/10 p-4 rounded-lg mb-4">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center">
                                <span class="text-2xl mr-2">${getMoodEmoji(mood.mood)}</span>
                                <h4 class="text-white font-semibold">${mood.mood}</h4>
                                <span class="ml-2 text-yellow-300">强度: ${mood.intensity}/10</span>
                            </div>
                            <span class="text-white/60 text-sm">${formatTime(mood.created_at)}</span>
                        </div>
                        <p class="text-white/80 text-sm mb-2">${mood.description}</p>
                        ${mood.factors && mood.factors.length > 0 ?
                            `<div class="flex flex-wrap gap-1">
                                ${mood.factors.map(factor => `<span class="bg-blue-500/20 text-blue-200 px-2 py-1 rounded text-xs">${factor}</span>`).join('')}
                            </div>` : ''}
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载心情记录失败:', error);
            }
        }

        document.getElementById('moodIntensity').addEventListener('input', (e) => {
            document.getElementById('intensityValue').textContent = e.target.value;
        });

        document.getElementById('moodForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const mood = document.getElementById('moodType').value;
            const intensity = parseInt(document.getElementById('moodIntensity').value);
            const description = document.getElementById('moodDescription').value.trim();
            const factorsStr = document.getElementById('moodFactors').value.trim();
            const factors = factorsStr ? factorsStr.split(',').map(f => f.trim()) : [];

            try {
                await api.call('CreateMoodRecord', mood, intensity, description, factors);
                closeMoodModal();
                loadMoods();
                alert('心情记录保存成功！');
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        });

        // 愿望清单相关功能
        function showAddWish() {
            document.getElementById('wishModal').classList.remove('hidden');
        }

        function closeWishModal() {
            document.getElementById('wishModal').classList.add('hidden');
            document.getElementById('wishForm').reset();
        }

        async function loadWishes() {
            try {
                const wishes = await api.call('GetAllWishlists');
                const wishList = document.getElementById('wishList');

                if (!wishes || wishes.length === 0) {
                    wishList.innerHTML = '<p class="text-white/60 text-center py-8">还没有愿望，添加一个共同目标吧！</p>';
                    return;
                }

                wishList.innerHTML = wishes.map(wish => `
                    <div class="bg-white/10 p-4 rounded-lg mb-4">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="text-white font-semibold ${wish.is_completed ? 'line-through opacity-50' : ''}">${wish.title}</h4>
                            <div class="flex items-center space-x-2">
                                ${wish.cost > 0 ? `<span class="text-green-300 text-sm">¥${wish.cost}</span>` : ''}
                                ${!wish.is_completed ?
                                    `<button onclick="completeWish('${wish.id}')" class="text-green-400 hover:text-green-300">
                                        <i class="fas fa-check"></i>
                                    </button>` : ''}
                            </div>
                        </div>
                        <p class="text-white/80 text-sm mb-3">${wish.description}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="text-purple-300 text-sm">${getCategoryEmoji(wish.category)} ${wish.category}</span>
                                <div class="flex items-center text-yellow-300">
                                    ${Array(wish.priority).fill('⭐').join('')}
                                </div>
                            </div>
                            ${wish.target_date ? `<span class="text-blue-300 text-sm">目标: ${formatDate(wish.target_date)}</span>` : ''}
                        </div>
                        ${wish.is_completed ? `<div class="mt-2 text-green-400 text-sm">✅ 已完成于 ${formatDate(wish.completed_at)}</div>` : ''}
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载愿望清单失败:', error);
            }
        }

        document.getElementById('wishPriority').addEventListener('input', (e) => {
            document.getElementById('priorityValue').textContent = e.target.value;
        });

        document.getElementById('wishForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const title = document.getElementById('wishTitle').value.trim();
            const description = document.getElementById('wishDescription').value.trim();
            const category = document.getElementById('wishCategory').value;
            const priority = parseInt(document.getElementById('wishPriority').value);
            const cost = parseFloat(document.getElementById('wishCost').value) || 0;
            const targetDate = document.getElementById('wishTargetDate').value || '';

            try {
                await api.call('CreateWishlist', title, description, category, priority, cost, targetDate);
                closeWishModal();
                loadWishes();
                alert('愿望添加成功！');
            } catch (error) {
                alert('添加失败: ' + error.message);
            }
        });

        async function completeWish(wishId) {
            if (confirm('确认完成这个愿望吗？')) {
                try {
                    await api.call('CompleteWishlist', wishId);
                    loadWishes();
                } catch (error) {
                    alert('操作失败: ' + error.message);
                }
            }
        }

        // 照片相关功能
        function openPhotoModal() {
            document.getElementById('photoModal').classList.remove('hidden');
        }

        function closePhotoModal() {
            document.getElementById('photoModal').classList.add('hidden');
            document.getElementById('photoForm').reset();
        }

        async function loadPhotos() {
            try {
                const photos = await api.call('GetAllPhotos');
                const photoGallery = document.getElementById('photoGallery');

                if (!photos || photos.length === 0) {
                    photoGallery.innerHTML = '<p class="col-span-full text-white/60 text-center py-8">还没有照片，上传第一张回忆吧！</p>';
                    return;
                }

                photoGallery.innerHTML = photos.map(photo => `
                    <div class="bg-white/10 rounded-lg overflow-hidden hover:bg-white/20 transition-all duration-300">
                        <div class="aspect-video bg-black/10 relative overflow-hidden">
                            ${photo.file_path ? `<img src="${photo.file_path}" alt="${photo.title}" class="absolute inset-0 w-full h-full object-cover" />` : `
                            <div class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-pink-500/20 to-purple-500/20">
                                <i class="fas fa-image text-4xl text-white/50"></i>
                                <span class="ml-2 text-white/70 text-sm">图片: ${photo.title}</span>
                            </div>
                            `}
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-white font-semibold">${photo.title}</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="deletePhoto('${photo.id}')" class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            ${photo.description ? `<p class="text-white/80 text-sm mb-3">${photo.description}</p>` : ''}
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-purple-300">${getCategoryEmoji(photo.category)} ${getCategoryName(photo.category)}</span>
                                <span class="text-white/50">${formatDate(photo.created_at)}</span>
                            </div>
                            ${photo.tags && photo.tags.length > 0 ?
                                `<div class="mt-2">
                                    <div class="flex flex-wrap gap-1">
                                        ${photo.tags.map(tag => `<span class="px-2 py-1 bg-white/10 rounded text-xs text-white/70">#${tag}</span>`).join('')}
                                    </div>
                                </div>` : ''}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载照片失败:', error);
                const photoGallery = document.getElementById('photoGallery');
                photoGallery.innerHTML = '<p class="col-span-full text-white/60 text-center py-8">加载照片时出现错误</p>';
            }
        }

        document.getElementById('photoForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const fileInput = document.getElementById('photoFile');
            const title = document.getElementById('photoTitle').value.trim();
            const description = document.getElementById('photoDescription').value.trim();
            const category = document.getElementById('photoCategory').value;
            const tagsInput = document.getElementById('photoTags').value.trim();
            const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

            if (!fileInput.files[0]) {
                alert('请选择一张照片');
                return;
            }

            try {
                // 将文件读取为Base64 Data URL，避免本地路径无法被WebView访问
                const file = fileInput.files[0];
                const filePath = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });

                await api.call('CreatePhoto', title, description, filePath, category, tags);
                closePhotoModal();
                loadPhotos();
                alert('照片上传成功！');
            } catch (error) {
                alert('上传失败: ' + error.message);
            }
        });

        async function deletePhoto(photoId) {
            if (confirm('确认删除这张照片吗？此操作不可撤销。')) {
                try {
                    await api.call('DeletePhoto', photoId);
                    loadPhotos();
                    alert('照片删除成功！');
                } catch (error) {
                    alert('删除失败: ' + error.message);
                }
            }
        }

        function getCategoryName(category) {
            const names = {
                daily: '日常',
                travel: '旅行',
                food: '美食',
                celebration: '庆祝',
                romantic: '浪漫',
                family: '家庭',
                pets: '宠物',
                other: '其他'
            };
            return names[category] || '其他';
        }

        // 设置相关功能
        async function loadSettings() {
            try {
                console.log('开始加载设置...');
                const settings = await api.call('GetSettings');
                console.log('获取到的设置数据:', settings);
                
                if (settings) {
                    // 加载基本设置 - 使用正确的字段名（JSON标签名）
                    document.getElementById('settingsTheme').value = settings.theme || 'romantic';
                    document.getElementById('settingsLanguage').value = settings.language || 'zh-CN';
                    // 这些字段在Go模型中没有定义，使用默认值
                    document.getElementById('settingsFontSize').value = 'medium';
                    document.getElementById('settingsDensity').value = 'comfortable';

                    // 加载通知设置 - 使用正确的字段名
                    document.getElementById('anniversaryNotifications').checked = settings.notification_enabled !== false;
                    document.getElementById('dailyReminders').checked = settings.notification_enabled !== false;

                    // 加载隐私设置 - 使用正确的字段名
                    document.getElementById('storageType').value = (settings.redis_enabled ? 'redis' : 'local');
                    document.getElementById('redisURL').value = settings.redis_url || '';
                    // 切换Redis配置区可见性
                    document.getElementById('redisConfig').classList.toggle('hidden', document.getElementById('storageType').value !== 'redis');
                    document.getElementById('autoBackup').checked = settings.backup_enabled !== false;
                    
                    console.log('设置加载成功');
                } else {
                    console.log('没有获取到设置数据，使用默认设置');
                    resetSettingsUI();
                }
            } catch (error) {
                console.error('加载设置失败:', error);
                // 使用默认设置
                resetSettingsUI();
            }
        }

        function resetSettingsUI() {
            document.getElementById('settingsTheme').value = 'romantic';
            document.getElementById('settingsLanguage').value = 'zh-CN';
            document.getElementById('settingsFontSize').value = 'medium';
            document.getElementById('settingsDensity').value = 'comfortable';
            document.getElementById('anniversaryNotifications').checked = true;
            document.getElementById('dailyReminders').checked = true;
            document.getElementById('storageType').value = 'local';
            document.getElementById('autoBackup').checked = true;
        }

        async function saveSettings() {
            try {
                const theme = document.getElementById('settingsTheme').value;
                const language = document.getElementById('settingsLanguage').value;
                const fontSize = document.getElementById('settingsFontSize').value;
                const density = document.getElementById('settingsDensity').value;
                const storageType = document.getElementById('storageType').value;
                const redisURL = document.getElementById('redisURL').value.trim();
                const anniversaryNotifications = document.getElementById('anniversaryNotifications').checked;
                const dailyReminders = document.getElementById('dailyReminders').checked;
                const autoBackup = document.getElementById('autoBackup').checked;

                console.log('准备保存设置:', {
                    theme, language, fontSize, density, storageType, 
                    redisURL, anniversaryNotifications, dailyReminders, autoBackup
                });

                await api.call('SaveSettings', theme, language, fontSize, density, storageType, redisURL, anniversaryNotifications, dailyReminders, autoBackup);
                
                console.log('设置保存成功');
                alert('设置保存成功！');

                // 应用主题与语言（即时生效）
                applyTheme(theme);
                applyLanguage(language);

            } catch (error) {
                console.error('保存设置失败:', error);
                alert('保存设置失败: ' + error.message);
            }
        }

        function applyTheme(theme) {
            const body = document.body;
            // 移除现有主题类
            body.classList.remove('theme-romantic', 'theme-purple', 'theme-blue', 'theme-gradient');
            // 应用新主题
            if (theme && theme !== 'romantic') {
                body.classList.add(`theme-${theme}`);
            }
        }

        function applyLanguage(lang) {
            // 简单示例：切换页面标题和几个关键文字。完整的i18n可接入字典表。
            const textMap = {
                'zh-CN': {
                    appTitle: 'Love Companion - 爱情伴侣',
                },
                'en-US': {
                    appTitle: 'Love Companion',
                },
                'ja-JP': {
                    appTitle: 'Love Companion（ラブコンパニオン）',
                }
            };
            const t = textMap[lang] || textMap['zh-CN'];
            document.title = t.appTitle;
        }

        async function resetSettings() {
            if (confirm('确认重置所有设置为默认值吗？')) {
                try {
                    resetSettingsUI();
                    await saveSettings();
                    alert('设置已重置为默认值！');
                } catch (error) {
                    alert('重置设置失败: ' + error.message);
                }
            }
        }

        // 工具函数
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        function formatTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function getMoodEmoji(mood) {
            const emojis = {
                happy: '😊',
                love: '💖',
                excited: '🎉',
                peaceful: '😌',
                sad: '😢',
                anxious: '😰',
                grateful: '🙏',
                romantic: '🌹'
            };
            return emojis[mood] || '😊';
        }

        function getCategoryEmoji(category) {
            const emojis = {
                love: '💖',
                birthday: '🎂',
                anniversary: '💍',
                holiday: '🎉',
                special: '⭐',
                travel: '✈️',
                gift: '🎁',
                activity: '🎯',
                home: '🏠',
                career: '💼',
                other: '📝'
            };
            return emojis[category] || '📝';
        }

        // 浮动爱心效果
        function createFloatingHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 6 + 's';
            heart.style.animationDuration = (Math.random() * 3 + 6) + 's';

            document.getElementById('floatingHearts').appendChild(heart);

            setTimeout(() => {
                heart.remove();
            }, 9000);
        }

        // 每3秒创建一个浮动爱心
        setInterval(createFloatingHeart, 3000);

        // 初始化一些爱心
        for (let i = 0; i < 3; i++) {
            setTimeout(createFloatingHeart, i * 1000);
        }

        // 应用启动时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 检查是否在Wails环境中，等待Wails运行时加载
            if (typeof window.go !== 'undefined') {
                // 等待一下确保Wails完全加载
                setTimeout(() => {
                    initApp();
                }, 100);
            } else {
                // 等待Wails运行时加载
                let retryCount = 0;
                const maxRetries = 50; // 5秒超时

                const checkWails = () => {
                    if (typeof window.go !== 'undefined' && window.go.main && window.go.main.App) {
                        initApp();
                    } else if (retryCount < maxRetries) {
                        retryCount++;
                        setTimeout(checkWails, 100);
                    } else {
                        // 超时后显示错误或开发模式
                        console.warn('Wails运行时加载超时，可能在开发环境中');
                        document.getElementById('initModal').style.display = 'none';
                    }
                };

                checkWails();
            }
        });
    </script>
</body>
</html>