# Redis服务启动脚本 (PowerShell)
Write-Host "启动Redis服务..." -ForegroundColor Green
Write-Host ""

# 检查Redis是否已安装
try {
    $redisPath = Get-Command redis-server -ErrorAction Stop
    Write-Host "Redis已安装: $($redisPath.Source)" -ForegroundColor Yellow
} catch {
    Write-Host "Redis未安装，请先安装Redis for Windows" -ForegroundColor Red
    Write-Host "下载地址: https://github.com/microsoftarchive/redis/releases" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按回车键退出"
    exit 1
}

# 检查Redis是否已在运行
$redisRunning = Get-NetTCPConnection -LocalPort 6379 -ErrorAction SilentlyContinue
if ($redisRunning) {
    Write-Host "Redis服务已在运行 (端口6379)" -ForegroundColor Green
    Write-Host ""
    Read-Host "按回车键退出"
    exit 0
}

# 启动Redis服务
Write-Host "正在启动Redis服务..." -ForegroundColor Yellow
Start-Process redis-server -WindowStyle Minimized

# 等待服务启动
Start-Sleep -Seconds 3

# 检查服务状态
$redisRunning = Get-NetTCPConnection -LocalPort 6379 -ErrorAction SilentlyContinue
if ($redisRunning) {
    Write-Host "Redis服务启动成功！" -ForegroundColor Green
    Write-Host "连接地址: redis://localhost:6379" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "现在可以启动Love Companion应用了" -ForegroundColor Green
    
    # 设置环境变量
    $env:REDIS_URL = "redis://localhost:6379"
    Write-Host "已设置环境变量 REDIS_URL=redis://localhost:6379" -ForegroundColor Cyan
} else {
    Write-Host "Redis服务启动失败，请检查安装" -ForegroundColor Red
}

Write-Host ""
Read-Host "按回车键退出"
