# Redis配置文件 - Love Companion应用
# 基于Redis 7.x优化配置

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 客户端配置
maxclients 10000

# 安全配置
# requirepass your_password_here  # 取消注释并设置密码

# 性能优化
tcp-backlog 511
databases 16
hz 10
