package main

import (
	"context"
	"embed"
	"fmt"
	"os"
	"time"
	"love-companion/app/services"
	"love-companion/app/models"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

// App struct
type App struct {
	ctx         context.Context
	dataService *services.DataService
}

// NewApp creates a new App application struct
func NewApp() *App {
	// 强制使用Redis存储，从环境变量获取Redis配置
	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		// 默认Redis连接字符串
		redisURL = "redis://localhost:6379"
	}

	// 初始化数据服务 (强制使用Redis存储)
	dataService := services.NewDataService("", redisURL, true)

	return &App{
		dataService: dataService,
	}
}

// OnStartup is called when the app starts and the context is saved
func (a *App) OnStartup(ctx context.Context) {
	a.ctx = ctx
	// 强制使用Redis存储，无需本地引导
	if a.dataService != nil {
		// 验证Redis连接
		if err := a.dataService.ValidateRedisConnection(); err != nil {
			panic("Redis连接失败: " + err.Error())
		}
		
		// 检查是否已有用户数据
		if a.dataService.HasExistingUser() {
			fmt.Println("检测到现有用户数据，正在恢复...")
		} else {
			fmt.Println("未检测到用户数据，需要初始化用户信息")
		}
	}
}

// OnShutdown is called when the app is about to quit
func (a *App) OnShutdown(ctx context.Context) {
	if a.dataService != nil {
		a.dataService.Close()
	}
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return "Hello " + name + ", Welcome to Love Companion!"
}

// TestConnection 测试系统连接状态
func (a *App) TestConnection() (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 测试Redis连接
	if err := a.dataService.ValidateRedisConnection(); err != nil {
		result["redis"] = map[string]interface{}{
			"status": "error",
			"message": err.Error(),
		}
	} else {
		result["redis"] = map[string]interface{}{
			"status": "success",
			"message": "Redis连接正常",
		}
	}

	// 测试用户信息
	user, err := a.dataService.GetCurrentUser()
	if err != nil {
		result["user"] = map[string]interface{}{
			"status": "error",
			"message": "用户信息获取失败: " + err.Error(),
		}
	} else if user == nil {
		result["user"] = map[string]interface{}{
			"status": "warning",
			"message": "用户未初始化",
		}
	} else {
		result["user"] = map[string]interface{}{
			"status": "success",
			"message": "用户信息正常",
		}
	}

	return result, nil
}

// GetCurrentUser 获取当前用户信息
func (a *App) GetCurrentUser() (interface{}, error) {
	return a.dataService.GetCurrentUser()
}

// InitUser 初始化用户信息
func (a *App) InitUser(name, partner, startDate string) error {
	startTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return err
	}
	return a.dataService.InitUser(name, partner, startTime)
}

// CreateDiary 创建新日记
func (a *App) CreateDiary(title, content, mood string, tags []string) (interface{}, error) {
	return a.dataService.CreateDiary(title, content, mood, tags)
}

// GetAllDiaries 获取所有日记
func (a *App) GetAllDiaries() (interface{}, error) {
	return a.dataService.GetAllDiaries()
}

// DeleteDiary 删除日记
func (a *App) DeleteDiary(diaryID string) error {
	return a.dataService.DeleteDiary(diaryID)
}

// GetAllAnniversaries 获取所有纪念日
func (a *App) GetAllAnniversaries() (interface{}, error) {
	return a.dataService.GetAllAnniversaries()
}

// GetAllWishlists 获取所有愿望清单
func (a *App) GetAllWishlists() (interface{}, error) {
	return a.dataService.GetAllWishlists()
}

// CreateAnniversary 创建纪念日
func (a *App) CreateAnniversary(title, description string, date string, isRecurring bool, remindDays int, category string) (interface{}, error) {
	dateTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, err
	}
	return a.dataService.CreateAnniversary(title, description, dateTime, isRecurring, remindDays, category)
}

// CreateWishlist 创建愿望清单
func (a *App) CreateWishlist(title, description, category string, priority int, cost float64, targetDate string) (interface{}, error) {
	var targetTime *time.Time
	if targetDate != "" {
		t, err := time.Parse("2006-01-02", targetDate)
		if err != nil {
			return nil, err
		}
		targetTime = &t
	}
	return a.dataService.CreateWishlist(title, description, category, priority, cost, targetTime)
}

// CompleteWishlist 完成愿望清单项目
func (a *App) CompleteWishlist(wishlistID string) error {
	return a.dataService.CompleteWishlist(wishlistID)
}

// CreateMoodRecord 创建心情记录
func (a *App) CreateMoodRecord(mood string, intensity int, description string, factors []string) (interface{}, error) {
	return a.dataService.CreateMoodRecord(mood, intensity, description, factors)
}

// GetMoodRecords 获取指定日期的心情记录
func (a *App) GetMoodRecords(date string) (interface{}, error) {
	dateTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, err
	}
	return a.dataService.GetMoodRecords(dateTime)
}

// GetAllMoods 获取所有心情记录
func (a *App) GetAllMoods() (interface{}, error) {
	return a.dataService.GetAllMoods()
}

// CreatePhoto 创建照片记录
func (a *App) CreatePhoto(title, description, filePath, category string, tags []string) (interface{}, error) {
	return a.dataService.CreatePhoto(title, description, filePath, category, tags)
}

// GetAllPhotos 获取所有照片
func (a *App) GetAllPhotos() (interface{}, error) {
	return a.dataService.GetAllPhotos()
}

// DeletePhoto 删除照片
func (a *App) DeletePhoto(photoID string) error {
	return a.dataService.DeletePhoto(photoID)
}

// UpdatePhoto 更新照片信息
func (a *App) UpdatePhoto(photoID, title, description, category string, tags []string) error {
	return a.dataService.UpdatePhoto(photoID, title, description, category, tags)
}

// UpdateDiary 更新日记
func (a *App) UpdateDiary(diaryID, title, content, mood string, tags []string) error {
	return a.dataService.UpdateDiary(diaryID, title, content, mood, tags)
}

// GetSettings 获取设置
func (a *App) GetSettings() (interface{}, error) {
	return a.dataService.GetSettings()
}

// SaveSettings 保存设置 (强制使用Redis)
func (a *App) SaveSettings(theme, language, fontSize, density, storageType, redisURL string, anniversaryNotifications, dailyReminders, autoBackup bool) error {
	user, err := a.dataService.GetCurrentUser()
	if err != nil {
		return err
	}

	settings := &models.Settings{
		UserID:              user.ID,
		Theme:               theme,
		Language:            language,
		NotificationEnabled: anniversaryNotifications || dailyReminders,
		BackupEnabled:       autoBackup,
		RedisEnabled:        true, // 强制启用Redis
		RedisURL:            redisURL,
	}

	return a.dataService.SaveSettings(settings)
}

// GetDaysCount 获取在一起的天数
func (a *App) GetDaysCount() (int, error) {
	user, err := a.dataService.GetCurrentUser()
	if err != nil {
		return 0, err
	}

	days := int(time.Since(user.StartDate).Hours() / 24)
	return days, nil
}

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "Love Companion - 爱情伴侣",
		Width:  1200,
		Height: 800,
		MinWidth: 800,
		MinHeight: 600,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 102, G: 126, B: 234, A: 255},
		OnStartup:  app.OnStartup,
		OnShutdown: app.OnShutdown,
		Bind: []interface{}{app}, // 绑定 App，前端可以通过 window.go.main.App 调用
	})

	if err != nil {
		println("Error:", err.Error())
	}
}