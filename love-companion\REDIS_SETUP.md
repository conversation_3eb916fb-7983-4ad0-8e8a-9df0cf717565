# Redis存储设置指南

## 概述

Love Companion应用现在完全使用Redis数据库进行数据存储，不再使用本地JSON文件。这提供了更好的数据一致性、性能和可扩展性。

## 前置要求

1. **安装Redis服务器**
   - Windows: 下载并安装 [Redis for Windows](https://github.com/microsoftarchive/redis/releases)
   - 或者使用Docker: `docker run -d -p 6379:6379 redis:latest`

2. **确保Redis服务运行**
   - Windows: 启动Redis服务
   - Docker: 容器正在运行

## 配置Redis连接

### 方法1: 环境变量（推荐）

在系统环境变量中设置 `REDIS_URL`：

```bash
# Windows PowerShell
$env:REDIS_URL="redis://localhost:6379"

# Windows CMD
set REDIS_URL=redis://localhost:6379
```

### 方法2: 系统环境变量

1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中添加 `REDIS_URL`
3. 值设置为 `redis://localhost:6379`

### 方法3: 应用内设置

首次启动应用时，在设置中配置Redis连接字符串。

## Redis连接字符串格式

```
redis://[username:password@]host[:port][/database_number]
```

### 常见示例

- **本地无密码**: `redis://localhost:6379`
- **本地带密码**: `redis://:password@localhost:6379`
- **指定数据库**: `redis://localhost:6379/0`
- **远程服务器**: `redis://username:<EMAIL>:6379/0`

## 数据迁移

如果您之前使用本地JSON存储，需要手动迁移数据：

1. 启动应用并连接到Redis
2. 重新创建用户信息
3. 重新添加日记、纪念日等数据

## 故障排除

### 连接失败

1. 检查Redis服务是否运行
2. 验证连接字符串格式
3. 检查防火墙设置
4. 确认端口6379是否开放

### 数据丢失

1. 检查Redis服务状态
2. 验证Redis配置
3. 检查磁盘空间

## 性能优化

1. **内存配置**: 为Redis分配足够内存
2. **持久化**: 配置RDB和AOF持久化
3. **网络**: 使用本地Redis或低延迟网络

## 安全建议

1. **密码保护**: 在生产环境中设置强密码
2. **网络隔离**: 限制Redis访问IP
3. **SSL/TLS**: 在公网环境中启用加密

## 监控和维护

1. **内存使用**: 监控Redis内存占用
2. **连接数**: 检查活跃连接数
3. **性能指标**: 监控命令执行时间
4. **备份**: 定期备份Redis数据

## 支持

如果遇到问题，请检查：
1. Redis服务状态
2. 连接配置
3. 应用日志
4. Redis日志
