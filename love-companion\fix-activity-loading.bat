@echo off
echo ========================================
echo Love Companion 活动加载问题修复工具
echo ========================================
echo.

echo 🔍 正在诊断问题...
echo.

REM 检查Redis服务状态
echo 1. 检查Redis服务状态...
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis服务正在运行
    set REDIS_OK=1
) else (
    echo ❌ Redis服务未运行
    set REDIS_OK=0
)

echo.

REM 如果Redis未运行，尝试启动
if %REDIS_OK%==0 (
    echo 🚀 正在启动Redis服务...
    start /min cmd /c "redis-server redis.conf"
    timeout /t 5 >nul
    
    REM 再次检查
    redis-cli ping >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Redis服务启动成功
        set REDIS_OK=1
    ) else (
        echo ❌ Redis服务启动失败
        echo.
        echo 💡 手动启动建议:
        echo    1. 检查端口6379是否被占用
        echo    2. 运行: redis-server redis.conf
        echo    3. 检查redis.conf配置文件
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 2. 设置环境变量...
set REDIS_URL=redis://localhost:6379
echo ✅ REDIS_URL 已设置为: %REDIS_URL%

echo.
echo 3. 检查数据完整性...
redis-cli exists user:index >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 用户索引存在
) else (
    echo ⚠️  用户索引不存在，可能需要重新初始化用户
    echo.
    echo 🔧 是否运行数据迁移工具? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo 正在运行数据迁移...
        if exist migrate-data.bat (
            call migrate-data.bat
        ) else (
            echo ❌ 数据迁移工具不存在
        )
    )
)

echo.
echo 4. 测试基本连接...
redis-cli set test:connection "ok" >nul 2>&1
redis-cli get test:connection >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis读写测试通过
    redis-cli del test:connection >nul 2>&1
) else (
    echo ❌ Redis读写测试失败
)

echo.
echo 5. 检查应用程序...
if exist love-companion.exe (
    echo ✅ 应用程序文件存在
) else (
    echo ⚠️  应用程序文件不存在，可能需要重新编译
)

echo.
echo ========================================
echo 🎯 修复建议
echo ========================================

if %REDIS_OK%==1 (
    echo ✅ Redis服务正常
    echo.
    echo 📱 现在可以启动应用程序:
    echo    方法1: 双击 love-companion.exe
    echo    方法2: 运行 wails dev (开发模式)
    echo    方法3: 运行 test-ui-improvements.bat
    echo.
    echo 🔄 如果问题仍然存在:
    echo    1. 在应用中点击"系统检查"按钮
    echo    2. 查看浏览器控制台错误信息 (F12)
    echo    3. 尝试重新初始化用户信息
    echo    4. 查看 TROUBLESHOOTING.md 获取详细帮助
) else (
    echo ❌ Redis服务问题需要手动解决
    echo.
    echo 🛠️  手动修复步骤:
    echo    1. 检查Redis是否正确安装
    echo    2. 检查端口6379是否被占用
    echo    3. 查看Redis日志文件
    echo    4. 尝试重新安装Redis
)

echo.
echo 📚 更多帮助:
echo    - 查看 TROUBLESHOOTING.md
echo    - 查看 UI_IMPROVEMENTS.md
echo    - 运行系统内置的诊断工具

echo.
pause
