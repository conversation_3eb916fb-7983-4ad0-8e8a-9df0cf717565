// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CompleteWishlist(arg1) {
  return window['go']['main']['App']['CompleteWishlist'](arg1);
}

export function CreateAnniversary(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['CreateAnniversary'](arg1, arg2, arg3, arg4, arg5, arg6);
}

export function CreateDiary(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['CreateDiary'](arg1, arg2, arg3, arg4);
}

export function CreateMoodRecord(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['CreateMoodRecord'](arg1, arg2, arg3, arg4);
}

export function CreatePhoto(arg1, arg2, arg3, arg4, arg5) {
  return window['go']['main']['App']['CreatePhoto'](arg1, arg2, arg3, arg4, arg5);
}

export function CreateWishlist(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['CreateWishlist'](arg1, arg2, arg3, arg4, arg5, arg6);
}

export function DeleteDiary(arg1) {
  return window['go']['main']['App']['DeleteDiary'](arg1);
}

export function DeletePhoto(arg1) {
  return window['go']['main']['App']['DeletePhoto'](arg1);
}

export function GetAllAnniversaries() {
  return window['go']['main']['App']['GetAllAnniversaries']();
}

export function GetAllDiaries() {
  return window['go']['main']['App']['GetAllDiaries']();
}

export function GetAllMoods() {
  return window['go']['main']['App']['GetAllMoods']();
}

export function GetAllPhotos() {
  return window['go']['main']['App']['GetAllPhotos']();
}

export function GetAllWishlists() {
  return window['go']['main']['App']['GetAllWishlists']();
}

export function GetCurrentUser() {
  return window['go']['main']['App']['GetCurrentUser']();
}

export function GetDaysCount() {
  return window['go']['main']['App']['GetDaysCount']();
}

export function GetMoodRecords(arg1) {
  return window['go']['main']['App']['GetMoodRecords'](arg1);
}

export function GetSettings() {
  return window['go']['main']['App']['GetSettings']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function InitUser(arg1, arg2, arg3) {
  return window['go']['main']['App']['InitUser'](arg1, arg2, arg3);
}

export function SaveSettings(arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9) {
  return window['go']['main']['App']['SaveSettings'](arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9);
}

export function UpdateDiary(arg1, arg2, arg3, arg4, arg5) {
  return window['go']['main']['App']['UpdateDiary'](arg1, arg2, arg3, arg4, arg5);
}

export function UpdatePhoto(arg1, arg2, arg3, arg4, arg5) {
  return window['go']['main']['App']['UpdatePhoto'](arg1, arg2, arg3, arg4, arg5);
}
