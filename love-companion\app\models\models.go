package models

import (
	"time"
)

// User 用户信息
type User struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Avatar   string    `json:"avatar"`
	Partner  string    `json:"partner"`
	StartDate time.Time `json:"start_date"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Diary 日记条目
type Diary struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	Mood      string    `json:"mood"` // happy, sad, excited, love, etc.
	Tags      []string  `json:"tags"`
	Photos    []string  `json:"photos"`
	Weather   string    `json:"weather"`
	Location  string    `json:"location"`
	IsPublic  bool      `json:"is_public"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Anniversary 纪念日
type Anniversary struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Date        time.Time `json:"date"`
	IsRecurring bool      `json:"is_recurring"`
	RemindDays  int       `json:"remind_days"` // 提前几天提醒
	Category    string    `json:"category"`   // love, birthday, holiday, etc.
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Photo 照片
type Photo struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	FilePath    string    `json:"file_path"`
	Category    string    `json:"category"`
	Tags        []string  `json:"tags"`
	TakenAt     time.Time `json:"taken_at"`
	Location    string    `json:"location"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MoodRecord 心情记录
type MoodRecord struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	Mood        string    `json:"mood"`        // 心情类型
	Intensity   int       `json:"intensity"`   // 强度 1-10
	Description string    `json:"description"` // 心情描述
	Factors     []string  `json:"factors"`     // 影响因素
	CreatedAt   time.Time `json:"created_at"`
}

// Wishlist 愿望清单
type Wishlist struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Category    string    `json:"category"` // travel, gift, activity, etc.
	Priority    int       `json:"priority"` // 优先级 1-5
	IsCompleted bool      `json:"is_completed"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	TargetDate  *time.Time `json:"target_date,omitempty"`
	Cost        float64   `json:"cost"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Settings 应用设置
type Settings struct {
	UserID           string `json:"user_id"`
	Theme            string `json:"theme"`             // light, dark, auto
	Language         string `json:"language"`          // zh-CN, en-US
	NotificationEnabled bool `json:"notification_enabled"`
	BackupEnabled    bool   `json:"backup_enabled"`
	DataPath         string `json:"data_path"`
	RedisEnabled     bool   `json:"redis_enabled"`
	RedisURL         string `json:"redis_url"`
	UpdatedAt        time.Time `json:"updated_at"`
}