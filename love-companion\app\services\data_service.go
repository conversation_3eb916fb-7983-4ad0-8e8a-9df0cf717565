package services

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"
	"love-companion/app/models"
	"love-companion/app/storage"
)

type DataService struct {
	redisStorage *storage.RedisStorage
	currentUser  *models.User
}

func NewDataService(dataPath string, redisURL string, useRedis bool) *DataService {
	ds := &DataService{}

	// 强制使用Redis存储
	if redisURL == "" {
		panic("Redis URL不能为空，必须提供Redis连接配置")
	}
	
	ds.redisStorage = storage.NewRedisStorage(redisURL)

	return ds
}

// 验证Redis连接
func (ds *DataService) ValidateRedisConnection() error {
	if ds.redisStorage == nil {
		return fmt.Errorf("Redis存储未初始化")
	}
	return ds.redisStorage.Ping()
}

// 生成唯一ID
func (ds *DataService) generateID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// 用户管理
func (ds *DataService) InitUser(name, partner string, startDate time.Time) error {
	user := &models.User{
		ID:        ds.generateID(),
		Name:      name,
		Partner:   partner,
		StartDate: startDate,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	ds.currentUser = user

	// 保存用户到Redis
	err := ds.redisStorage.SaveUser(user)
	if err != nil {
		return err
	}

	// 同时保存到用户索引中，方便后续查找
	return ds.redisStorage.SaveUserToIndex(user)
}

func (ds *DataService) GetCurrentUser() (*models.User, error) {
	if ds.currentUser != nil {
		return ds.currentUser, nil
	}

	// 尝试从Redis恢复用户数据
	user, err := ds.redisStorage.LoadUserFromIndex()
	if err != nil {
		return nil, err
	}

	ds.currentUser = user
	return user, nil
}

// 检查是否已有用户数据
func (ds *DataService) HasExistingUser() bool {
	user, err := ds.redisStorage.LoadUserFromIndex()
	return err == nil && user != nil
}

// 日记管理
func (ds *DataService) CreateDiary(title, content, mood string, tags []string) (*models.Diary, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	diary := &models.Diary{
		ID:        ds.generateID(),
		UserID:    user.ID,
		Title:     title,
		Content:   content,
		Mood:      mood,
		Tags:      tags,
		IsPublic:  false,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = ds.redisStorage.SaveDiary(diary)

	return diary, err
}

func (ds *DataService) GetAllDiaries() ([]models.Diary, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadAllDiaries(user.ID)
}

func (ds *DataService) UpdateDiary(diaryID, title, content, mood string, tags []string) error {
	diaries, err := ds.GetAllDiaries()
	if err != nil {
		return err
	}

	for i, diary := range diaries {
		if diary.ID == diaryID {
			diaries[i].Title = title
			diaries[i].Content = content
			diaries[i].Mood = mood
			diaries[i].Tags = tags
			diaries[i].UpdatedAt = time.Now()

			return ds.redisStorage.SaveDiary(&diaries[i])
		}
	}

	return fmt.Errorf("未找到指定的日记")
}

func (ds *DataService) DeleteDiary(diaryID string) error {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return err
	}
	return ds.redisStorage.DeleteDiary(user.ID, diaryID)
}

// 纪念日管理
func (ds *DataService) CreateAnniversary(title, description string, date time.Time, isRecurring bool, remindDays int, category string) (*models.Anniversary, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	anniversary := &models.Anniversary{
		ID:          ds.generateID(),
		UserID:      user.ID,
		Title:       title,
		Description: description,
		Date:        date,
		IsRecurring: isRecurring,
		RemindDays:  remindDays,
		Category:    category,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = ds.redisStorage.SaveAnniversary(anniversary)

	return anniversary, err
}

func (ds *DataService) GetAllAnniversaries() ([]models.Anniversary, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadAllAnniversaries(user.ID)
}

// 心情记录管理
func (ds *DataService) CreateMoodRecord(mood string, intensity int, description string, factors []string) (*models.MoodRecord, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	moodRecord := &models.MoodRecord{
		ID:          ds.generateID(),
		UserID:      user.ID,
		Mood:        mood,
		Intensity:   intensity,
		Description: description,
		Factors:     factors,
		CreatedAt:   time.Now(),
	}

	err = ds.redisStorage.SaveMoodRecord(moodRecord)

	return moodRecord, err
}

func (ds *DataService) GetMoodRecords(date time.Time) ([]models.MoodRecord, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadMoodRecords(user.ID, date)
}

// GetAllMoods 获取所有心情记录
func (ds *DataService) GetAllMoods() ([]models.MoodRecord, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadAllMoodRecords(user.ID)
}

// 照片管理
func (ds *DataService) CreatePhoto(title, description, filePath, category string, tags []string) (*models.Photo, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	photo := &models.Photo{
		ID:          ds.generateID(),
		UserID:      user.ID,
		Title:       title,
		Description: description,
		FilePath:    filePath,
		Category:    category,
		Tags:        tags,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = ds.redisStorage.SavePhoto(photo)

	return photo, err
}

func (ds *DataService) GetAllPhotos() ([]models.Photo, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadAllPhotos(user.ID)
}

func (ds *DataService) UpdatePhoto(photoID, title, description, category string, tags []string) error {
	photos, err := ds.GetAllPhotos()
	if err != nil {
		return err
	}

	for i, photo := range photos {
		if photo.ID == photoID {
			photos[i].Title = title
			photos[i].Description = description
			photos[i].Category = category
			photos[i].Tags = tags
			photos[i].UpdatedAt = time.Now()

			return ds.redisStorage.SavePhoto(&photos[i])
		}
	}

	return fmt.Errorf("未找到指定的照片")
}

func (ds *DataService) DeletePhoto(photoID string) error {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return err
	}
	return ds.redisStorage.DeletePhoto(user.ID, photoID)
}

// 愿望清单管理
func (ds *DataService) CreateWishlist(title, description, category string, priority int, cost float64, targetDate *time.Time) (*models.Wishlist, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	wishlist := &models.Wishlist{
		ID:          ds.generateID(),
		UserID:      user.ID,
		Title:       title,
		Description: description,
		Category:    category,
		Priority:    priority,
		IsCompleted: false,
		Cost:        cost,
		TargetDate:  targetDate,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = ds.redisStorage.SaveWishlist(wishlist)

	return wishlist, err
}

func (ds *DataService) GetAllWishlists() ([]models.Wishlist, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, err
	}
	return ds.redisStorage.LoadAllWishlists(user.ID)
}

func (ds *DataService) CompleteWishlist(wishlistID string) error {
	wishlists, err := ds.GetAllWishlists()
	if err != nil {
		return err
	}

	for i, wish := range wishlists {
		if wish.ID == wishlistID {
			now := time.Now()
			wishlists[i].IsCompleted = true
			wishlists[i].CompletedAt = &now
			wishlists[i].UpdatedAt = now

			return ds.redisStorage.SaveWishlist(&wishlists[i])
		}
	}

	return fmt.Errorf("未找到指定的愿望清单项目")
}

// 设置管理
func (ds *DataService) GetSettings() (*models.Settings, error) {
	user, err := ds.GetCurrentUser()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	return ds.redisStorage.LoadSettings(user.ID)
}

func (ds *DataService) SaveSettings(settings *models.Settings) error {
	settings.UpdatedAt = time.Now()
	// 直接保存到Redis
	return ds.redisStorage.SaveSettings(settings)
}

// 关闭服务
func (ds *DataService) Close() error {
	if ds.redisStorage != nil {
		return ds.redisStorage.Close()
	}
	return nil
}