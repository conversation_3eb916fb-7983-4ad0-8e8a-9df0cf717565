# Love Companion Redis存储问题解决方案

## 🎯 问题描述

您遇到的问题是：应用已经连接到Redis数据库，数据也存储在Redis中，但每次启动应用都要求重新输入用户信息，而不是从Redis读取已有的数据。

## 🔍 问题分析

### 根本原因
1. **缺少用户索引机制**：应用无法找到Redis中已有的用户数据
2. **硬编码用户ID**：代码中使用了固定的用户ID，无法动态识别现有用户
3. **数据存储结构不完整**：缺少用户数据的索引和查找机制

### 技术细节
- Redis中存储了用户数据（如 `user:e169ef4d7dcbb2ff6558d980000f5014`）
- 但应用启动时无法找到这些数据
- 每次都需要重新创建用户，导致数据丢失

## ✅ 解决方案

### 1. 完全移除本地JSON存储
- 修改 `main.go`：强制使用Redis存储
- 修改 `data_service.go`：移除所有JSON存储相关代码
- 应用启动时自动连接Redis

### 2. 添加用户索引机制
- 在Redis中创建用户索引：`user:index:main`
- 新增 `SaveUserToIndex()` 方法：保存用户到索引
- 新增 `LoadUserFromIndex()` 方法：从索引加载用户

### 3. 智能用户数据检测
- 应用启动时自动检查Redis中是否有用户数据
- 如果有数据，自动恢复用户信息
- 如果没有数据，提示需要初始化

## 🚀 使用方法

### 步骤1：运行数据迁移工具
```bash
# 在love-companion目录下运行
migrate-data.bat
```

这个工具会：
- 连接到您的Redis数据库
- 查找现有的用户数据
- 创建必要的用户索引
- 检查其他数据类型

### 步骤2：启动应用
```bash
# 设置环境变量
$env:REDIS_URL="redis://localhost:6379"

# 启动应用
wails dev
```

### 步骤3：验证效果
- 应用启动时不再要求输入用户信息
- 自动显示已有的日记、纪念日等数据
- 所有历史数据正常恢复

## 📁 修改的文件

### 核心代码修改
- `main.go` - 强制使用Redis存储，添加用户数据检测
- `app/services/data_service.go` - 移除JSON存储，强制使用Redis
- `app/storage/redis_storage.go` - 添加用户索引管理功能

### 新增工具文件
- `tools/migrate-data.go` - 数据迁移工具源码
- `migrate-data.bat` - 数据迁移工具批处理文件
- `tools/` - 工具目录，避免与主应用冲突

### 配置文件
- `docker-compose.yml` - Redis容器配置
- `redis.conf` - Redis优化配置
- `start-redis.bat` / `start-redis.ps1` - Redis启动脚本

## 🔧 技术实现

### 用户索引机制
```go
// 保存用户到索引
func (rs *RedisStorage) SaveUserToIndex(user *models.User) error {
    // 保存用户数据
    err := rs.SaveUser(user)
    if err != nil {
        return err
    }
    
    // 保存到用户索引，使用固定的键名
    return rs.client.Set(rs.ctx, "user:index:main", user.ID, 0).Err()
}

// 从索引加载用户
func (rs *RedisStorage) LoadUserFromIndex() (*models.User, error) {
    // 从索引中获取用户ID
    userID, err := rs.client.Get(rs.ctx, "user:index:main").Result()
    if err != nil {
        return nil, fmt.Errorf("获取用户索引失败: %v", err)
    }
    
    // 根据用户ID加载用户数据
    return rs.LoadUser(userID)
}
```

### 智能启动检测
```go
func (a *App) OnStartup(ctx context.Context) {
    a.ctx = ctx
    
    if a.dataService != nil {
        // 验证Redis连接
        if err := a.dataService.ValidateRedisConnection(); err != nil {
            panic("Redis连接失败: " + err.Error())
        }
        
        // 检查是否已有用户数据
        if a.dataService.HasExistingUser() {
            fmt.Println("检测到现有用户数据，正在恢复...")
        } else {
            fmt.Println("未检测到用户数据，需要初始化用户信息")
        }
    }
}
```

## 🎉 预期效果

### 迁移前
- ❌ 每次启动都要求输入用户信息
- ❌ 无法访问历史数据
- ❌ 用户体验差

### 迁移后
- ✅ 应用启动时自动检测用户数据
- ✅ 自动恢复所有历史数据
- ✅ 无需重复输入用户信息
- ✅ 数据完全存储在Redis中，性能更好

## 🐛 故障排除

### 如果迁移后仍有问题
1. **检查Redis连接**：确认Redis服务正在运行
2. **验证数据迁移**：重新运行 `migrate-data.bat`
3. **检查Redis数据**：使用RESP.app查看数据是否正确
4. **查看应用日志**：检查启动时的错误信息

### 常见错误
- `Redis连接失败`：检查Redis服务状态
- `获取用户索引失败`：运行数据迁移工具
- `用户数据序列化失败`：检查Redis数据完整性

## 📞 技术支持

如果遇到问题，请按以下顺序检查：
1. Redis服务状态
2. 数据迁移是否成功
3. 环境变量配置
4. 应用启动日志
5. Redis数据完整性

## 🎯 总结

通过这次修改，您的Love Companion应用现在：
- **完全使用Redis存储**：不再有任何本地保存功能
- **智能数据恢复**：启动时自动检测和恢复用户数据
- **性能优化**：Redis提供更好的数据访问性能
- **数据一致性**：所有数据集中存储在Redis中

运行数据迁移工具后，应用就能正常读取Redis中的现有数据，不再要求重复输入用户信息了！
