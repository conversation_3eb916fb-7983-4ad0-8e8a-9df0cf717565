package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/go-redis/redis/v8"
)

// 数据迁移工具
// 用于将现有的Redis数据迁移到新的索引结构

func main() {
	// 获取Redis连接配置
	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		redisURL = "redis://localhost:6379"
	}

	fmt.Printf("正在连接到Redis: %s\n", redisURL)

	// 解析Redis URL
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		log.Fatalf("Redis URL 解析失败: %v", err)
	}

	// 创建Redis客户端
	client := redis.NewClient(opt)
	ctx := context.Background()

	// 测试连接
	_, err = client.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Redis 连接失败: %v", err)
	}

	fmt.Println("✅ Redis连接成功")

	// 迁移用户数据
	if err := migrateUserData(ctx, client); err != nil {
		log.Printf("⚠️ 用户数据迁移失败: %v", err)
	} else {
		fmt.Println("✅ 用户数据迁移完成")
	}

	// 迁移其他数据
	if err := migrateOtherData(ctx, client); err != nil {
		log.Printf("⚠️ 其他数据迁移失败: %v", err)
	} else {
		fmt.Println("✅ 其他数据迁移完成")
	}

	fmt.Println("\n🎉 数据迁移完成！")
	fmt.Println("现在可以启动Love Companion应用了")
}

// 迁移用户数据
func migrateUserData(ctx context.Context, client *redis.Client) error {
	fmt.Println("正在迁移用户数据...")

	// 查找所有用户键
	pattern := "user:*"
	keys, err := client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("查找用户键失败: %v", err)
	}

	// 过滤掉索引键
	var userKeys []string
	for _, key := range keys {
		if key != "user:index:main" {
			userKeys = append(userKeys, key)
		}
	}

	if len(userKeys) == 0 {
		fmt.Println("  未找到用户数据")
		return nil
	}

	fmt.Printf("  找到 %d 个用户数据\n", len(userKeys))

	// 为每个用户创建索引
	for _, key := range userKeys {
		// 提取用户ID
		userID := key[5:] // 去掉 "user:" 前缀
		
		// 检查是否已有索引
		exists, err := client.Exists(ctx, "user:index:main").Result()
		if err != nil {
			continue
		}

		if exists == 0 {
			// 创建用户索引
			err = client.Set(ctx, "user:index:main", userID, 0).Err()
			if err != nil {
				fmt.Printf("    ⚠️ 为用户 %s 创建索引失败: %v\n", userID, err)
			} else {
				fmt.Printf("    ✅ 为用户 %s 创建索引成功\n", userID)
			}
		} else {
			fmt.Printf("    ℹ️ 用户索引已存在\n")
		}
		break // 只处理第一个用户
	}

	return nil
}

// 迁移其他数据
func migrateOtherData(ctx context.Context, client *redis.Client) error {
	fmt.Println("正在检查其他数据...")

	// 检查日记数据
	diaryKeys, err := client.Keys(ctx, "diary:*").Result()
	if err == nil && len(diaryKeys) > 0 {
		fmt.Printf("  📝 找到 %d 条日记数据\n", len(diaryKeys))
	}

	// 检查纪念日数据
	anniversaryKeys, err := client.Keys(ctx, "anniversary:*").Result()
	if err == nil && len(anniversaryKeys) > 0 {
		fmt.Printf("  🎉 找到 %d 条纪念日数据\n", len(anniversaryKeys))
	}

	// 检查照片数据
	photoKeys, err := client.Keys(ctx, "photo:*").Result()
	if err == nil && len(photoKeys) > 0 {
		fmt.Printf("  📸 找到 %d 条照片数据\n", len(photoKeys))
	}

	// 检查心情记录
	moodKeys, err := client.Keys(ctx, "mood:*").Result()
	if err == nil && len(moodKeys) > 0 {
		fmt.Printf("  😊 找到 %d 条心情记录\n", len(moodKeys))
	}

	// 检查愿望清单
	wishlistKeys, err := client.Keys(ctx, "wishlist:*").Result()
	if err == nil && len(wishlistKeys) > 0 {
		fmt.Printf("  ⭐ 找到 %d 条愿望清单\n", len(wishlistKeys))
	}

	// 检查设置数据
	settingsKeys, err := client.Keys(ctx, "settings:*").Result()
	if err == nil && len(settingsKeys) > 0 {
		fmt.Printf("  ⚙️ 找到 %d 条设置数据\n", len(settingsKeys))
	}

	return nil
}
