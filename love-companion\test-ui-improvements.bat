@echo off
echo ========================================
echo Love Companion UI 改进测试脚本
echo ========================================
echo.

echo 1. 检查 Redis 服务状态...
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis 服务正在运行
) else (
    echo ❌ Redis 服务未运行，正在启动...
    start /min cmd /c "redis-server redis.conf"
    timeout /t 3 >nul
    echo ✅ Redis 服务已启动
)

echo.
echo 2. 设置环境变量...
set REDIS_URL=redis://localhost:6379
echo ✅ REDIS_URL 已设置为: %REDIS_URL%

echo.
echo 3. 启动应用程序...
echo 📱 正在启动 Love Companion...
echo.
echo 🎨 UI 改进功能:
echo    - 美化的玻璃态效果和动画
echo    - 实时更新的最近活动
echo    - 优化的交互体验
echo    - 响应式动画效果
echo.
echo 🔄 实时更新功能:
echo    - 每30秒自动更新活动
echo    - 数据操作后立即刷新
echo    - 智能页面切换管理
echo.

wails dev

echo.
echo 应用程序已关闭
pause
