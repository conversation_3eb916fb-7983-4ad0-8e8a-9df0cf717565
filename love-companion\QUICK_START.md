# Love Companion 快速启动指南

## 🚀 快速开始

### 方法1: 使用Docker (推荐)

1. **启动Redis服务**
   ```bash
   docker-compose up -d
   ```

2. **验证Redis运行状态**
   ```bash
   docker-compose ps
   ```

3. **数据迁移 (重要！)**
   ```bash
   # 如果Redis中已有数据，需要先迁移
   migrate-data.bat
   ```

4. **启动应用**
   ```bash
   # 设置环境变量
   $env:REDIS_URL="redis://localhost:6379"
   
   # 运行应用
   go run main.go
   ```

### 方法2: 本地Redis安装

1. **下载并安装Redis for Windows**
   - 访问: https://github.com/microsoftarchive/redis/releases
   - 下载最新版本并安装

2. **启动Redis服务**
   ```bash
   # 使用批处理文件
   start-redis.bat
   
   # 或使用PowerShell
   .\start-redis.ps1
   
   # 或手动启动
   redis-server
   ```

3. **数据迁移 (重要！)**
   ```bash
   # 如果Redis中已有数据，需要先迁移
   migrate-data.bat
   ```

4. **启动应用**
   ```bash
   # 设置环境变量
   $env:REDIS_URL="redis://localhost:6379"
   
   # 运行应用
   go run main.go
   ```

## 🔧 环境配置

### 设置环境变量

#### Windows PowerShell
```powershell
$env:REDIS_URL="redis://localhost:6379"
```

#### Windows CMD
```cmd
set REDIS_URL=redis://localhost:6379
```

#### 系统环境变量 (永久)
1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中添加 `REDIS_URL`
3. 值设置为 `redis://localhost:6379`

## 📊 验证Redis连接

### 使用Redis CLI测试
```bash
# 连接到Redis
redis-cli

# 测试连接
127.0.0.1:6379> ping
PONG

# 查看数据库信息
127.0.0.1:6379> info
```

### 使用应用测试
启动应用后，如果看到"Redis连接失败"错误，说明连接配置有问题。

## 🔄 数据迁移 (解决重复输入问题)

### 问题描述
如果您之前使用过应用，Redis中可能已有数据，但应用每次启动都要求重新输入用户信息。

### 解决方案
运行数据迁移工具，将现有数据迁移到新的索引结构：

```bash
# 运行数据迁移
migrate-data.bat
```

### 迁移过程
1. 连接到Redis数据库
2. 查找现有用户数据
3. 创建用户索引 (`user:index:main`)
4. 检查其他数据类型
5. 完成迁移

### 迁移后效果
- 应用启动时自动检测到现有用户数据
- 不再要求重新输入用户信息
- 所有历史数据正常显示

## 🐛 常见问题

### 1. Redis连接失败
- 检查Redis服务是否运行
- 验证连接字符串格式
- 检查防火墙设置
- 确认端口6379是否开放

### 2. 应用要求重复输入用户信息
- 运行数据迁移工具：`migrate-data.bat`
- 检查Redis中是否有用户数据
- 确认用户索引是否正确创建

### 3. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :6379

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 4. 权限问题
- 以管理员身份运行PowerShell/CMD
- 检查Redis安装目录权限

## 📁 文件说明

- `docker-compose.yml` - Docker容器配置
- `redis.conf` - Redis配置文件
- `start-redis.bat` - Windows Redis启动脚本
- `start-redis.ps1` - PowerShell Redis启动脚本
- `migrate-data.go` - 数据迁移工具源码
- `migrate-data.bat` - 数据迁移工具批处理文件
- `redis-config.example` - 环境变量配置示例

## 🔄 数据迁移

如果您之前使用本地JSON存储：
1. 备份原有数据文件
2. 启动Redis服务
3. 运行数据迁移工具
4. 重新启动应用

## 📞 技术支持

遇到问题请检查：
1. Redis服务状态
2. 网络连接
3. 环境变量配置
4. 应用日志
5. 是否运行了数据迁移工具

## 🎯 下一步

成功启动后，您可以：
1. 创建用户账户
2. 添加日记和纪念日
3. 管理愿望清单
4. 记录心情变化
5. 上传照片回忆
