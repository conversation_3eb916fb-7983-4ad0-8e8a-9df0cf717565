@echo off
echo 启动Redis服务...
echo.

REM 检查Redis是否已安装
where redis-server >nul 2>nul
if %errorlevel% neq 0 (
    echo Redis未安装，请先安装Redis for Windows
    echo 下载地址: https://github.com/microsoftarchive/redis/releases
    echo.
    pause
    exit /b 1
)

REM 检查Redis是否已在运行
netstat -an | findstr ":6379" >nul
if %errorlevel% equ 0 (
    echo Redis服务已在运行 (端口6379)
    echo.
    pause
    exit /b 0
)

REM 启动Redis服务
echo 正在启动Redis服务...
start /min redis-server

REM 等待服务启动
timeout /t 3 /nobreak >nul

REM 检查服务状态
netstat -an | findstr ":6379" >nul
if %errorlevel% equ 0 (
    echo Redis服务启动成功！
    echo 连接地址: redis://localhost:6379
    echo.
    echo 现在可以启动Love Companion应用了
) else (
    echo Redis服务启动失败，请检查安装
)

echo.
pause
