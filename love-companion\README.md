# 🌹 Love Companion - 爱情伴侣软件开发文档

## 📋 项目概述

**项目名称**: Love Companion (爱情伴侣)  
**版本**: 2.0.0  
**开发框架**: Wails v2 + Go + HTML5  
**UI风格**: Glassmorphism (毛玻璃效果)  
**数据存储**: Redis数据库 (高性能内存数据库)  

## 🎯 功能特性

### 核心功能模块
1. **恋爱日记本** - 记录美好回忆和日常生活
2. **纪念日管理** - 重要日期提醒和倒计时
3. **照片相册** - 专属回忆相册管理
4. **心情记录** - 情感状态追踪和分析
5. **愿望清单** - 共同目标和计划管理
6. **个人设置** - 主题、通知、数据存储配置

### UI设计特色
- **Glassmorphism风格**: 毛玻璃效果，现代化视觉体验
- **Inter字体**: 清晰易读的现代字体
- **响应式设计**: 适配不同屏幕尺寸
- **浮动爱心动画**: 浪漫的背景交互效果
- **渐变色彩**: 温馨的紫粉色调搭配

## 🏗 技术架构

### 前端技术栈
```
HTML5 + CSS3 + JavaScript (ES6+)
├── UI框架: Tailwind CSS (CDN)
├── 图标: Font Awesome 6.4.0
├── 字体: Inter Font Family
└── 动效: CSS3 Animations & Transitions
```

### 后端技术栈
```
Go 1.22
├── 桌面框架: Wails v2
├── 数据存储: Redis数据库 (高性能内存存储)
├── 运行时: WebView2 (Windows) / WebKit (Linux) / WKWebView (macOS)
└── 依赖管理: Go Modules
```

### 项目结构
```
love-companion/
├── main.go                 # 应用程序入口
├── wails.json             # Wails配置文件
├── go.mod                 # Go模块依赖
├── docker-compose.yml     # Redis容器配置
├── redis.conf             # Redis配置文件
├── start-redis.bat        # Windows Redis启动脚本
├── start-redis.ps1        # PowerShell Redis启动脚本
├── app/                   # 后端应用逻辑
│   ├── models/           # 数据模型定义
│   │   └── models.go
│   ├── storage/          # 数据存储层
│   │   └── redis_storage.go   # Redis数据库存储
│   └── services/         # 业务逻辑服务
│       └── data_service.go
└── frontend/             # 前端资源
    └── dist/
        └── index.html    # 主界面
```

## 🚀 快速开始

### 前置要求
- Go 1.22+
- Redis 7.x
- Windows 10/11 (支持Wails v2)

### 启动步骤

1. **启动Redis服务**
   ```bash
   # 使用Docker (推荐)
   docker-compose up -d
   
   # 或使用启动脚本
   start-redis.bat
   ```

2. **设置环境变量**
   ```bash
   $env:REDIS_URL="redis://localhost:6379"
   ```

3. **启动应用**
   ```bash
   go run main.go
   ```

详细配置请参考 [QUICK_START.md](./QUICK_START.md) 和 [REDIS_SETUP.md](./REDIS_SETUP.md)

## 📊 数据模型设计

### 核心实体模型

#### User (用户)
```go
type User struct {
    ID        string    // 用户唯一标识
    Name      string    // 用户姓名
    Avatar    string    // 头像路径
    Partner   string    // 伴侣姓名
    StartDate time.Time // 开始交往日期
    CreatedAt time.Time // 创建时间
    UpdatedAt time.Time // 更新时间
}
```

#### Diary (日记)
```go
type Diary struct {
    ID        string    // 日记ID
    UserID    string    // 用户ID
    Title     string    // 标题
    Content   string    // 内容
    Mood      string    // 心情标签
    Tags      []string  // 标签列表
    Photos    []string  // 关联照片
    Weather   string    // 天气信息
    Location  string    // 地理位置
    IsPublic  bool      // 是否公开
    CreatedAt time.Time // 创建时间
    UpdatedAt time.Time // 更新时间
}
```

#### Anniversary (纪念日)
```go
type Anniversary struct {
    ID          string    // 纪念日ID
    UserID      string    // 用户ID
    Title       string    // 标题
    Description string    // 描述
    Date        time.Time // 日期
    IsRecurring bool      // 是否重复
    RemindDays  int       // 提前提醒天数
    Category    string    // 分类
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 数据存储方案

#### Redis存储 (默认)
- **优点**: 高性能、支持并发、数据持久化
- **配置**: 支持自定义Redis连接URL
- **数据结构**: 使用Hash和Set结构优化查询性能

## 🔧 开发环境配置

### 环境要求
- Go 1.21+
- Node.js 16+ (用于前端构建)
- Git
- Windows 10+ / macOS 10.13+ / Linux

### 依赖安装
```bash
# 1. 安装Wails CLI
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 2. 验证安装
wails doctor

# 3. 进入项目目录
cd love-companion

# 4. 安装Go依赖
go mod tidy

# 5. 开发模式运行
wails dev
```

### Redis配置 (可选)
```bash
# Windows (使用Docker)
docker run -d --name redis-love -p 6379:6379 redis:7-alpine

# 或本地安装Redis
# 下载: https://github.com/microsoftarchive/redis/releases
```

## 🚀 构建与部署

### 开发构建
```bash
# 开发模式 (热重载)
wails dev

# 构建测试版本
wails build -debug
```

### 生产构建
```bash
# 构建生产版本
wails build

# 构建完成后文件位置:
# Windows: build/bin/love-companion.exe
# macOS: build/bin/Love Companion.app
# Linux: build/bin/love-companion
```

### 打包发布
```bash
# Windows - 创建安装程序
wails build -platform windows -nsis

# macOS - 创建DMG
wails build -platform darwin -dmg

# Linux - 创建AppImage
wails build -platform linux -appimage
```

## 🎨 UI界面设计

### 色彩方案
```css
/* 主要渐变背景 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 毛玻璃效果 */
background: rgba(255, 255, 255, 0.25);
backdrop-filter: blur(10px);
border: 1px solid rgba(255, 255, 255, 0.18);

/* 强调色彩 */
--pink-accent: #FFB6C1;    /* 粉色 */
--purple-accent: #9B59B6;  /* 紫色 */
--blue-accent: #3498DB;    /* 蓝色 */
```

### 交互动效
- **页面切换**: 平滑过渡动画
- **卡片悬停**: 轻微上浮效果
- **浮动爱心**: 持续上升动画
- **按钮点击**: 涟漪效果

### 响应式设计
- **桌面**: 1200x800 最佳显示
- **小屏**: 自适应布局调整
- **侧边栏**: 可折叠设计

## 🔐 安全与隐私

### 数据安全
- **本地存储**: 数据完全存储在用户设备
- **加密选项**: 支持敏感数据加密存储
- **备份功能**: 支持数据导出和恢复

### 隐私保护
- **离线运行**: 无需网络连接
- **无数据收集**: 不上传任何用户数据
- **开源透明**: 代码完全开放可审查

## 🧪 测试策略

### 单元测试
```bash
# 运行Go单元测试
go test ./app/...

# 测试覆盖率
go test -cover ./app/...
```

### 集成测试
```bash
# 测试数据存储层
go test ./app/storage -v

# 测试业务逻辑层
go test ./app/services -v
```

### UI测试
- **手动测试**: 功能完整性验证
- **跨平台测试**: Windows/macOS/Linux兼容性
- **性能测试**: 内存使用和响应速度

## 🐛 调试与日志

### 开发调试
```bash
# 启用详细日志
wails dev -debug

# 查看应用日志
tail -f ~/.wails/logs/love-companion.log
```

### 生产环境日志
- **日志位置**: `%APPDATA%/Love Companion/logs/`
- **日志级别**: INFO, WARN, ERROR
- **日志轮转**: 按日期自动归档

## 📈 性能优化

### 前端优化
- **资源压缩**: CSS/JS文件压缩
- **图片优化**: WebP格式支持
- **懒加载**: 大型组件按需加载

### 后端优化
- **内存管理**: 及时释放资源
- **数据缓存**: 常用数据内存缓存
- **并发控制**: 合理控制协程数量

## 🔄 版本控制

### Git工作流
```bash
# 主分支
main          # 生产稳定版本
develop       # 开发分支
feature/*     # 功能分支
hotfix/*      # 紧急修复分支
```

### 版本号规则
- **主版本号**: 重大功能变更
- **次版本号**: 新功能添加
- **修订号**: Bug修复

## 🚀 后续规划

### V1.1版本计划
- [ ] 数据同步功能
- [ ] 主题自定义
- [ ] 多语言支持
- [ ] 语音备忘录

### V1.2版本计划
- [ ] 云端备份
- [ ] 社交分享
- [ ] 统计分析
- [ ] 插件系统

---

**开发团队**: Love Tech  
**最后更新**: 2024-01-15  
**文档版本**: 1.0.0