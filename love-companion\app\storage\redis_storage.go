package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"love-companion/app/models"

	"github.com/go-redis/redis/v8"
)

type RedisStorage struct {
	client *redis.Client
	ctx    context.Context
}

func NewRedisStorage(redisURL string) *RedisStorage {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		panic(fmt.Sprintf("Redis URL 解析失败: %v", err))
	}

	client := redis.NewClient(opt)
	ctx := context.Background()

	// 测试连接
	_, err = client.Ping(ctx).Result()
	if err != nil {
		panic(fmt.Sprintf("Redis 连接失败: %v", err))
	}

	return &RedisStorage{
		client: client,
		ctx:    ctx,
	}
}

// Ping 测试Redis连接
func (rs *RedisStorage) Ping() error {
	_, err := rs.client.Ping(rs.ctx).Result()
	return err
}

// 用户相关操作
func (rs *RedisStorage) SaveUser(user *models.User) error {
	data, err := json.Marshal(user)
	if err != nil {
		return fmt.Errorf("用户数据序列化失败: %v", err)
	}

	return rs.client.Set(rs.ctx, "user:"+user.ID, data, 0).Err()
}

// SaveUserToIndex 保存用户到索引中，方便后续查找
func (rs *RedisStorage) SaveUserToIndex(user *models.User) error {
	// 保存用户数据
	err := rs.SaveUser(user)
	if err != nil {
		return err
	}

	// 保存到用户索引，使用固定的键名
	return rs.client.Set(rs.ctx, "user:index:main", user.ID, 0).Err()
}

func (rs *RedisStorage) LoadUser(userID string) (*models.User, error) {
	data, err := rs.client.Get(rs.ctx, "user:"+userID).Result()
	if err != nil {
		return nil, fmt.Errorf("获取用户数据失败: %v", err)
	}

	var user models.User
	err = json.Unmarshal([]byte(data), &user)
	return &user, err
}

// LoadUserFromIndex 从索引中加载用户数据
func (rs *RedisStorage) LoadUserFromIndex() (*models.User, error) {
	// 从索引中获取用户ID
	userID, err := rs.client.Get(rs.ctx, "user:index:main").Result()
	if err != nil {
		return nil, fmt.Errorf("获取用户索引失败: %v", err)
	}

	// 根据用户ID加载用户数据
	return rs.LoadUser(userID)
}

// 日记相关操作
func (rs *RedisStorage) SaveDiary(diary *models.Diary) error {
	data, err := json.Marshal(diary)
	if err != nil {
		return fmt.Errorf("日记数据序列化失败: %v", err)
	}

	// 保存日记数据
	err = rs.client.Set(rs.ctx, "diary:"+diary.ID, data, 0).Err()
	if err != nil {
		return err
	}

	// 添加到用户的日记列表
	return rs.client.SAdd(rs.ctx, "user:"+diary.UserID+":diaries", diary.ID).Err()
}

func (rs *RedisStorage) LoadAllDiaries(userID string) ([]models.Diary, error) {
	diaryIDs, err := rs.client.SMembers(rs.ctx, "user:"+userID+":diaries").Result()
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %v", err)
	}

	var diaries []models.Diary
	for _, id := range diaryIDs {
		data, err := rs.client.Get(rs.ctx, "diary:"+id).Result()
		if err != nil {
			continue // 跳过错误的条目
		}

		var diary models.Diary
		if err := json.Unmarshal([]byte(data), &diary); err == nil {
			diaries = append(diaries, diary)
		}
	}

	return diaries, nil
}

func (rs *RedisStorage) DeleteDiary(userID, diaryID string) error {
	// 从数据存储中删除
	err := rs.client.Del(rs.ctx, "diary:"+diaryID).Err()
	if err != nil {
		return err
	}

	// 从用户列表中移除
	return rs.client.SRem(rs.ctx, "user:"+userID+":diaries", diaryID).Err()
}

// 纪念日相关操作
func (rs *RedisStorage) SaveAnniversary(anniversary *models.Anniversary) error {
	data, err := json.Marshal(anniversary)
	if err != nil {
		return fmt.Errorf("纪念日数据序列化失败: %v", err)
	}

	err = rs.client.Set(rs.ctx, "anniversary:"+anniversary.ID, data, 0).Err()
	if err != nil {
		return err
	}

	return rs.client.SAdd(rs.ctx, "user:"+anniversary.UserID+":anniversaries", anniversary.ID).Err()
}

func (rs *RedisStorage) LoadAllAnniversaries(userID string) ([]models.Anniversary, error) {
	anniversaryIDs, err := rs.client.SMembers(rs.ctx, "user:"+userID+":anniversaries").Result()
	if err != nil {
		return nil, fmt.Errorf("获取纪念日列表失败: %v", err)
	}

	var anniversaries []models.Anniversary
	for _, id := range anniversaryIDs {
		data, err := rs.client.Get(rs.ctx, "anniversary:"+id).Result()
		if err != nil {
			continue
		}

		var anniversary models.Anniversary
		if err := json.Unmarshal([]byte(data), &anniversary); err == nil {
			anniversaries = append(anniversaries, anniversary)
		}
	}

	return anniversaries, nil
}

// 照片相关操作
func (rs *RedisStorage) SavePhoto(photo *models.Photo) error {
	data, err := json.Marshal(photo)
	if err != nil {
		return fmt.Errorf("照片数据序列化失败: %v", err)
	}

	err = rs.client.Set(rs.ctx, "photo:"+photo.ID, data, 0).Err()
	if err != nil {
		return err
	}

	return rs.client.SAdd(rs.ctx, "user:"+photo.UserID+":photos", photo.ID).Err()
}

func (rs *RedisStorage) LoadAllPhotos(userID string) ([]models.Photo, error) {
	photoIDs, err := rs.client.SMembers(rs.ctx, "user:"+userID+":photos").Result()
	if err != nil {
		return nil, fmt.Errorf("获取照片列表失败: %v", err)
	}

	var photos []models.Photo
	for _, id := range photoIDs {
		data, err := rs.client.Get(rs.ctx, "photo:"+id).Result()
		if err != nil {
			continue
		}

		var photo models.Photo
		if err := json.Unmarshal([]byte(data), &photo); err == nil {
			photos = append(photos, photo)
		}
	}

	return photos, nil
}

// 删除照片
func (rs *RedisStorage) DeletePhoto(userID, photoID string) error {
	// 删除照片数据
	if err := rs.client.Del(rs.ctx, "photo:"+photoID).Err(); err != nil {
		return err
	}
	// 从用户的照片集合中移除
	return rs.client.SRem(rs.ctx, "user:"+userID+":photos", photoID).Err()
}



// 心情记录相关操作
func (rs *RedisStorage) SaveMoodRecord(mood *models.MoodRecord) error {
	data, err := json.Marshal(mood)
	if err != nil {
		return fmt.Errorf("心情数据序列化失败: %v", err)
	}

	err = rs.client.Set(rs.ctx, "mood:"+mood.ID, data, 0).Err()
	if err != nil {
		return err
	}

	// 按日期索引心情记录
	dateKey := "user:" + mood.UserID + ":moods:" + mood.CreatedAt.Format("2006-01-02")
	return rs.client.SAdd(rs.ctx, dateKey, mood.ID).Err()
}

func (rs *RedisStorage) LoadMoodRecords(userID string, date time.Time) ([]models.MoodRecord, error) {
	dateKey := "user:" + userID + ":moods:" + date.Format("2006-01-02")
	moodIDs, err := rs.client.SMembers(rs.ctx, dateKey).Result()
	if err != nil {
		return nil, fmt.Errorf("获取心情记录失败: %v", err)
	}

	var moods []models.MoodRecord
	for _, id := range moodIDs {
		data, err := rs.client.Get(rs.ctx, "mood:"+id).Result()
		if err != nil {
			continue
		}

		var mood models.MoodRecord
		if err := json.Unmarshal([]byte(data), &mood); err == nil {
			moods = append(moods, mood)
		}
	}

	return moods, nil
}

// 加载某用户全部心情记录（不按日期过滤）
func (rs *RedisStorage) LoadAllMoodRecords(userID string) ([]models.MoodRecord, error) {
	// 获取该用户所有日期集合的键
	// 这里我们简单地扫描 keys: user:<uid>:moods:* （注意：生产环境更推荐维护一个日期索引集合）
	pattern := "user:" + userID + ":moods:*"
	var moods []models.MoodRecord
	iter := rs.client.Scan(rs.ctx, 0, pattern, 0).Iterator()
	for iter.Next(rs.ctx) {
		key := iter.Val()
		ids, err := rs.client.SMembers(rs.ctx, key).Result()
		if err != nil {
			continue
		}
		for _, id := range ids {
			data, err := rs.client.Get(rs.ctx, "mood:"+id).Result()
			if err != nil {
				continue
			}
			var m models.MoodRecord
			if err := json.Unmarshal([]byte(data), &m); err == nil {
				moods = append(moods, m)
			}
		}
	}
	if err := iter.Err(); err != nil {
		return nil, err
	}
	return moods, nil
}

// 愿望清单相关操作
func (rs *RedisStorage) SaveWishlist(wish *models.Wishlist) error {
	data, err := json.Marshal(wish)
	if err != nil {
		return fmt.Errorf("愿望数据序列化失败: %v", err)
	}

	err = rs.client.Set(rs.ctx, "wishlist:"+wish.ID, data, 0).Err()
	if err != nil {
		return err
	}

	return rs.client.SAdd(rs.ctx, "user:"+wish.UserID+":wishlists", wish.ID).Err()
}

func (rs *RedisStorage) LoadAllWishlists(userID string) ([]models.Wishlist, error) {
	wishIDs, err := rs.client.SMembers(rs.ctx, "user:"+userID+":wishlists").Result()
	if err != nil {
		return nil, fmt.Errorf("获取愿望清单失败: %v", err)
	}

	var wishes []models.Wishlist
	for _, id := range wishIDs {
		data, err := rs.client.Get(rs.ctx, "wishlist:"+id).Result()
		if err != nil {
			continue
		}

		var wish models.Wishlist
		if err := json.Unmarshal([]byte(data), &wish); err == nil {
			wishes = append(wishes, wish)
		}
	}

	return wishes, nil
}

// 设置相关操作
func (rs *RedisStorage) SaveSettings(settings *models.Settings) error {
	data, err := json.Marshal(settings)
	if err != nil {
		return fmt.Errorf("设置数据序列化失败: %v", err)
	}

	return rs.client.Set(rs.ctx, "settings:"+settings.UserID, data, 0).Err()
}

func (rs *RedisStorage) LoadSettings(userID string) (*models.Settings, error) {
	data, err := rs.client.Get(rs.ctx, "settings:"+userID).Result()
	if err != nil {
		return nil, fmt.Errorf("获取设置数据失败: %v", err)
	}

	var settings models.Settings
	err = json.Unmarshal([]byte(data), &settings)
	return &settings, err
}

// 关闭连接
func (rs *RedisStorage) Close() error {
	return rs.client.Close()
}