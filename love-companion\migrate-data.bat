@echo off
echo 数据迁移工具 - Love Companion
echo ================================
echo.

REM 设置环境变量
if "%REDIS_URL%"=="" (
    set REDIS_URL=redis://localhost:6379
    echo 使用默认Redis连接: %REDIS_URL%
) else (
    echo 使用环境变量Redis连接: %REDIS_URL%
)

echo.

REM 检查Go是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Go未安装，请先安装Go
    echo 下载地址: https://golang.org/dl/
    pause
    exit /b 1
)

echo ✅ Go已安装

REM 运行数据迁移
echo.
echo 正在运行数据迁移...
go run tools/migrate-data.go

echo.
pause
