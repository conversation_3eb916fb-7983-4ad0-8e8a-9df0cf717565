package storage

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"love-companion/app/models"
)

type JSONStorage struct {
	dataPath string
}

func NewJSONStorage(dataPath string) *JSONStorage {
	if err := os.<PERSON>dir<PERSON>ll(dataPath, 0755); err != nil {
		panic(fmt.Sprintf("无法创建数据目录: %v", err))
	}
	return &JSONStorage{dataPath: dataPath}
}

// 用户相关操作
func (js *JSONStorage) SaveUser(user *models.User) error {
	return js.saveToFile("user.json", user)
}

func (js *JSONStorage) LoadUser() (*models.User, error) {
	var user models.User
	err := js.loadFromFile("user.json", &user)
	return &user, err
}

// 日记相关操作
func (js *JSONStorage) SaveDiary(diary *models.Diary) error {
	diaries, err := js.LoadAllDiaries()
	if err != nil {
		diaries = []models.Diary{}
	}
	
	// 查找是否存在，更新或添加
	found := false
	for i, d := range diaries {
		if d.ID == diary.ID {
			diaries[i] = *diary
			found = true
			break
		}
	}
	if !found {
		diaries = append(diaries, *diary)
	}
	
	return js.saveToFile("diaries.json", diaries)
}

func (js *JSONStorage) LoadAllDiaries() ([]models.Diary, error) {
	var diaries []models.Diary
	err := js.loadFromFile("diaries.json", &diaries)
	return diaries, err
}

func (js *JSONStorage) DeleteDiary(diaryID string) error {
	diaries, err := js.LoadAllDiaries()
	if err != nil {
		return err
	}
	
	for i, diary := range diaries {
		if diary.ID == diaryID {
			diaries = append(diaries[:i], diaries[i+1:]...)
			break
		}
	}
	
	return js.saveToFile("diaries.json", diaries)
}

// 纪念日相关操作
func (js *JSONStorage) SaveAnniversary(anniversary *models.Anniversary) error {
	anniversaries, err := js.LoadAllAnniversaries()
	if err != nil {
		anniversaries = []models.Anniversary{}
	}
	
	found := false
	for i, a := range anniversaries {
		if a.ID == anniversary.ID {
			anniversaries[i] = *anniversary
			found = true
			break
		}
	}
	if !found {
		anniversaries = append(anniversaries, *anniversary)
	}
	
	return js.saveToFile("anniversaries.json", anniversaries)
}

func (js *JSONStorage) LoadAllAnniversaries() ([]models.Anniversary, error) {
	var anniversaries []models.Anniversary
	err := js.loadFromFile("anniversaries.json", &anniversaries)
	return anniversaries, err
}

// 照片相关操作
func (js *JSONStorage) SavePhoto(photo *models.Photo) error {
	photos, err := js.LoadAllPhotos()
	if err != nil {
		photos = []models.Photo{}
	}
	
	found := false
	for i, p := range photos {
		if p.ID == photo.ID {
			photos[i] = *photo
			found = true
			break
		}
	}
	if !found {
		photos = append(photos, *photo)
	}
	
	return js.saveToFile("photos.json", photos)
}

func (js *JSONStorage) LoadAllPhotos() ([]models.Photo, error) {
	var photos []models.Photo
	err := js.loadFromFile("photos.json", &photos)
	return photos, err
}

func (js *JSONStorage) DeletePhoto(photoID string) error {
	photos, err := js.LoadAllPhotos()
	if err != nil {
		return err
	}
	
	for i, photo := range photos {
		if photo.ID == photoID {
			photos = append(photos[:i], photos[i+1:]...)
			break
		}
	}
	
	return js.saveToFile("photos.json", photos)
}

// 心情记录相关操作
func (js *JSONStorage) SaveMoodRecord(mood *models.MoodRecord) error {
	moods, err := js.LoadAllMoodRecords()
	if err != nil {
		moods = []models.MoodRecord{}
	}
	
	found := false
	for i, m := range moods {
		if m.ID == mood.ID {
			moods[i] = *mood
			found = true
			break
		}
	}
	if !found {
		moods = append(moods, *mood)
	}
	
	return js.saveToFile("mood_records.json", moods)
}

func (js *JSONStorage) LoadAllMoodRecords() ([]models.MoodRecord, error) {
	var moods []models.MoodRecord
	err := js.loadFromFile("mood_records.json", &moods)
	return moods, err
}

// 愿望清单相关操作
func (js *JSONStorage) SaveWishlist(wish *models.Wishlist) error {
	wishes, err := js.LoadAllWishlists()
	if err != nil {
		wishes = []models.Wishlist{}
	}
	
	found := false
	for i, w := range wishes {
		if w.ID == wish.ID {
			wishes[i] = *wish
			found = true
			break
		}
	}
	if !found {
		wishes = append(wishes, *wish)
	}
	
	return js.saveToFile("wishlists.json", wishes)
}

func (js *JSONStorage) LoadAllWishlists() ([]models.Wishlist, error) {
	var wishes []models.Wishlist
	err := js.loadFromFile("wishlists.json", &wishes)
	return wishes, err
}

// 设置相关操作
func (js *JSONStorage) SaveSettings(settings *models.Settings) error {
	return js.saveToFile("settings.json", settings)
}

func (js *JSONStorage) LoadSettings() (*models.Settings, error) {
	var settings models.Settings
	err := js.loadFromFile("settings.json", &settings)
	return &settings, err
}

// 私有辅助方法
func (js *JSONStorage) saveToFile(filename string, data interface{}) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}
	
	filePath := filepath.Join(js.dataPath, filename)
	return ioutil.WriteFile(filePath, jsonData, 0644)
}

func (js *JSONStorage) loadFromFile(filename string, target interface{}) error {
	filePath := filepath.Join(js.dataPath, filename)
	
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filename)
	}
	
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}
	
	return json.Unmarshal(data, target)
}